#!/bin/bash
# 永嘉交通事故检测系统 - 统一快速启动脚本
# 在根目录方便地测试三个算法

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示主菜单
show_main_menu() {
    clear
    print_header "永嘉交通事故检测系统 - 统一管理"
    echo ""
    echo "🚗 算法选择:"
    echo "  1) 人车非检测算法 (renchefei)"
    echo "  2) 事故分类算法 (accident_classify)"
    echo "  3) 温州人脸识别算法 (wenzhou_face)"
    echo ""
    echo "🐳 Docker 管理:"
    echo "  4) 查看所有容器状态"
    echo "  5) 停止所有容器"
    echo "  6) 重启所有容器"
    echo ""
    echo "📊 系统信息:"
    echo "  7) 查看项目结构"
    echo "  8) 检查环境依赖"
    echo ""
    echo "  0) 退出"
    echo ""
}

# 运行指定算法
run_algorithm() {
    local algorithm=$1
    local algorithm_name=$2
    
    print_info "启动 $algorithm_name..."
    
    # 检查算法目录是否存在
    if [ ! -d "algorithms/$algorithm" ]; then
        print_error "算法目录不存在: algorithms/$algorithm"
        return 1
    fi
    
    # 检查脚本是否存在
    if [ ! -f "algorithms/$algorithm/scripts/quick_start.sh" ]; then
        print_error "快速启动脚本不存在: algorithms/$algorithm/scripts/quick_start.sh"
        return 1
    fi
    
    # 清除虚拟环境变量，避免路径冲突
    unset VIRTUAL_ENV
    
    print_info "正在启动 $algorithm_name 交互式菜单..."
    echo ""
    
    # 运行算法的快速启动脚本
    ./algorithms/$algorithm/scripts/quick_start.sh
}

# 查看所有容器状态
check_all_containers() {
    print_header "Docker 容器状态"
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 未运行，请先启动 Docker"
        return 1
    fi
    
    echo ""
    print_info "查找相关容器..."
    
    # 查找所有相关容器
    containers=$(docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" | grep -E "(renchefei|accident|wenzhou|face)" || true)
    
    if [ -z "$containers" ]; then
        print_warning "未找到相关的 Docker 容器"
    else
        echo ""
        echo "$containers"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 停止所有容器
stop_all_containers() {
    print_header "停止所有容器"
    
    if ! docker info &> /dev/null; then
        print_error "Docker 未运行"
        return 1
    fi
    
    # 查找并停止相关容器
    container_names=$(docker ps --format "{{.Names}}" | grep -E "(renchefei|accident|wenzhou|face)" || true)
    
    if [ -z "$container_names" ]; then
        print_warning "没有运行中的相关容器"
    else
        print_info "停止容器..."
        echo "$container_names" | while read container; do
            if [ -n "$container" ]; then
                print_info "停止容器: $container"
                docker stop "$container" || true
            fi
        done
        print_success "所有容器已停止"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 重启所有容器
restart_all_containers() {
    print_header "重启所有容器"
    
    if ! docker info &> /dev/null; then
        print_error "Docker 未运行"
        return 1
    fi
    
    # 查找相关容器
    container_names=$(docker ps -a --format "{{.Names}}" | grep -E "(renchefei|accident|wenzhou|face)" || true)
    
    if [ -z "$container_names" ]; then
        print_warning "没有找到相关容器"
    else
        print_info "重启容器..."
        echo "$container_names" | while read container; do
            if [ -n "$container" ]; then
                print_info "重启容器: $container"
                docker restart "$container" || true
            fi
        done
        print_success "所有容器已重启"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 查看项目结构
show_project_structure() {
    print_header "项目结构"
    echo ""
    
    if command -v tree &> /dev/null; then
        tree -L 3 -I '.venv|__pycache__|*.pyc|.git'
    else
        print_info "项目目录结构:"
        find . -type d -name ".venv" -prune -o -name "__pycache__" -prune -o -name ".git" -prune -o -type d -print | head -20 | sort
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 检查环境依赖
check_dependencies() {
    print_header "环境依赖检查"
    echo ""
    
    # 检查 Python
    if command -v python3 &> /dev/null; then
        python_version=$(python3 --version)
        print_success "Python: $python_version"
    else
        print_error "Python3 未安装"
    fi
    
    # 检查 uv
    if command -v uv &> /dev/null; then
        uv_version=$(uv --version)
        print_success "uv: $uv_version"
    else
        print_warning "uv 未安装 (推荐安装以获得更好的虚拟环境管理)"
    fi
    
    # 检查 Docker
    if command -v docker &> /dev/null; then
        if docker info &> /dev/null; then
            docker_version=$(docker --version)
            print_success "Docker: $docker_version (运行中)"
        else
            print_warning "Docker 已安装但未运行"
        fi
    else
        print_warning "Docker 未安装"
    fi
    
    # 检查虚拟环境
    echo ""
    print_info "虚拟环境检查:"
    for algorithm in renchefei accident_classify wenzhou_face; do
        if [ -d "algorithms/$algorithm/.venv" ]; then
            print_success "$algorithm: 虚拟环境存在"
        else
            print_warning "$algorithm: 虚拟环境不存在"
        fi
    done
    
    echo ""
    read -p "按回车键继续..."
}

# 主循环
main() {
    while true; do
        show_main_menu
        read -p "请选择 [0-8]: " choice
        
        case $choice in
            1)
                run_algorithm "renchefei" "人车非检测算法"
                ;;
            2)
                run_algorithm "accident_classify" "事故分类算法"
                ;;
            3)
                run_algorithm "wenzhou_face" "温州人脸识别算法"
                ;;
            4)
                check_all_containers
                ;;
            5)
                stop_all_containers
                ;;
            6)
                restart_all_containers
                ;;
            7)
                show_project_structure
                ;;
            8)
                check_dependencies
                ;;
            0)
                print_info "退出系统"
                exit 0
                ;;
            *)
                print_error "无效选择，请输入 0-8"
                sleep 2
                ;;
        esac
    done
}

# 启动主程序
main
