<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>License Server Demo - 授权API测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .demo-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            background: #fafafa;
        }

        .demo-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .response {
            margin-top: 20px;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            word-wrap: break-word;
            min-height: 60px;
            border: 2px solid #ddd;
        }

        .response.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .response.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .response.info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        .quick-test {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .quick-test button {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .quick-test button:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-valid {
            background: #28a745;
        }

        .status-invalid {
            background: #dc3545;
        }

        .status-unknown {
            background: #6c757d;
        }

        @media (max-width: 600px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 License Server Demo</h1>
            <p>极简授权API服务器测试工具</p>
        </div>

        <div class="content">
            <!-- License验证测试 -->
            <div class="demo-section">
                <h3>📋 License验证测试</h3>
                
                <div class="quick-test">
                    <button onclick="setLicenseKey('demo-key-123')">有效密钥</button>
                    <button onclick="setLicenseKey('test-key-456')">过期密钥</button>
                    <button onclick="setLicenseKey('premium-key-789')">高级密钥</button>
                    <button onclick="setLicenseKey('invalid-key-999')">无效密钥</button>
                </div>

                <div class="input-group">
                    <label for="licenseKey">License密钥:</label>
                    <input type="text" id="licenseKey" placeholder="输入要验证的license密钥" value="demo-key-123">
                </div>

                <div class="input-group">
                    <label for="serverUrl">服务器地址:</label>
                    <input type="text" id="serverUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
                </div>

                <button class="btn" onclick="verifyLicense()">🔍 验证License</button>
                <button class="btn btn-secondary" onclick="clearResponse('verifyResponse')">🗑️ 清空结果</button>

                <div id="verifyResponse" class="response">点击"验证License"按钮开始测试...</div>
            </div>

            <!-- 健康检查测试 -->
            <div class="demo-section">
                <h3>💚 健康检查测试</h3>
                
                <button class="btn" onclick="healthCheck()">🏥 健康检查</button>
                <button class="btn btn-secondary" onclick="clearResponse('healthResponse')">🗑️ 清空结果</button>

                <div id="healthResponse" class="response">点击"健康检查"按钮开始测试...</div>
            </div>

            <!-- 服务信息测试 -->
            <div class="demo-section">
                <h3>ℹ️ 服务信息测试</h3>
                
                <button class="btn" onclick="getServiceInfo()">📊 获取服务信息</button>
                <button class="btn btn-secondary" onclick="clearResponse('infoResponse')">🗑️ 清空结果</button>

                <div id="infoResponse" class="response">点击"获取服务信息"按钮开始测试...</div>
            </div>
        </div>
    </div>

    <script>
        // 设置license密钥
        function setLicenseKey(key) {
            document.getElementById('licenseKey').value = key;
        }

        // 清空响应
        function clearResponse(elementId) {
            const element = document.getElementById(elementId);
            element.textContent = '已清空...';
            element.className = 'response';
        }

        // 格式化JSON响应
        function formatResponse(data, isError = false) {
            try {
                if (typeof data === 'string') {
                    data = JSON.parse(data);
                }
                return JSON.stringify(data, null, 2);
            } catch (e) {
                return data.toString();
            }
        }

        // 显示响应结果
        function showResponse(elementId, data, isError = false, isSuccess = false) {
            const element = document.getElementById(elementId);
            element.textContent = formatResponse(data);
            
            if (isError) {
                element.className = 'response error';
            } else if (isSuccess) {
                element.className = 'response success';
            } else {
                element.className = 'response info';
            }
        }

        // 验证License
        async function verifyLicense() {
            const licenseKey = document.getElementById('licenseKey').value.trim();
            const serverUrl = document.getElementById('serverUrl').value.trim();
            
            if (!licenseKey) {
                showResponse('verifyResponse', '请输入License密钥', true);
                return;
            }

            try {
                showResponse('verifyResponse', '正在验证License...', false, false);
                
                const response = await fetch(`${serverUrl}/verify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        license_key: licenseKey
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    const isValid = data.status === 'valid';
                    const statusIcon = isValid ? '✅' : '❌';
                    const result = `${statusIcon} 验证结果:\n\n${JSON.stringify(data, null, 2)}`;
                    showResponse('verifyResponse', result, !isValid, isValid);
                } else {
                    showResponse('verifyResponse', `HTTP ${response.status}: ${JSON.stringify(data, null, 2)}`, true);
                }
            } catch (error) {
                showResponse('verifyResponse', `❌ 请求失败: ${error.message}\n\n请确保服务器正在运行在 ${serverUrl}`, true);
            }
        }

        // 健康检查
        async function healthCheck() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            
            try {
                showResponse('healthResponse', '正在检查服务器健康状态...', false, false);
                
                const response = await fetch(`${serverUrl}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    const isHealthy = data.status === 'healthy';
                    const statusIcon = isHealthy ? '💚' : '💔';
                    const result = `${statusIcon} 健康检查结果:\n\n${JSON.stringify(data, null, 2)}`;
                    showResponse('healthResponse', result, !isHealthy, isHealthy);
                } else {
                    showResponse('healthResponse', `HTTP ${response.status}: ${JSON.stringify(data, null, 2)}`, true);
                }
            } catch (error) {
                showResponse('healthResponse', `❌ 请求失败: ${error.message}\n\n请确保服务器正在运行在 ${serverUrl}`, true);
            }
        }

        // 获取服务信息
        async function getServiceInfo() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            
            try {
                showResponse('infoResponse', '正在获取服务信息...', false, false);
                
                const response = await fetch(`${serverUrl}/`);
                const data = await response.json();
                
                if (response.ok) {
                    const result = `📊 服务信息:\n\n${JSON.stringify(data, null, 2)}`;
                    showResponse('infoResponse', result, false, true);
                } else {
                    showResponse('infoResponse', `HTTP ${response.status}: ${JSON.stringify(data, null, 2)}`, true);
                }
            } catch (error) {
                showResponse('infoResponse', `❌ 请求失败: ${error.message}\n\n请确保服务器正在运行在 ${serverUrl}`, true);
            }
        }

        // 页面加载完成后自动进行健康检查
        window.addEventListener('load', function() {
            setTimeout(healthCheck, 1000);
        });
    </script>
</body>
</html>
