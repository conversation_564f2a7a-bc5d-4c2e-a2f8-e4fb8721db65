#!/usr/bin/env python3
"""
极简授权API服务器
提供license验证服务
"""

import json
import os
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel


# 数据模型
class LicenseRequest(BaseModel):
    license_key: str


class LicenseResponse(BaseModel):
    status: str
    message: str = ""
    expires_at: str = ""


# 创建FastAPI应用
app = FastAPI(
    title="License Server",
    description="极简的授权API服务器",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据文件路径
DATA_FILE = Path(__file__).parent / "license_data.json"


def load_license_data() -> Dict[str, Any]:
    """加载license数据"""
    if not DATA_FILE.exists():
        # 创建默认数据文件
        default_data = {
            "licenses": {
                "demo-key-123": {
                    "expires_at": "2025-12-31T23:59:59",
                    "description": "演示密钥"
                },
                "test-key-456": {
                    "expires_at": "2024-12-31T23:59:59", 
                    "description": "测试密钥（已过期）"
                }
            }
        }
        save_license_data(default_data)
        return default_data
    
    try:
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        raise HTTPException(status_code=500, detail=f"数据文件读取错误: {str(e)}")


def save_license_data(data: Dict[str, Any]) -> None:
    """保存license数据"""
    try:
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据文件保存错误: {str(e)}")


def is_license_valid(license_key: str, license_data: Dict[str, Any]) -> tuple[bool, str, str]:
    """
    验证license是否有效
    返回: (是否有效, 消息, 过期时间)
    """
    licenses = license_data.get("licenses", {})
    
    if license_key not in licenses:
        return False, "License密钥不存在", ""
    
    license_info = licenses[license_key]
    expires_at_str = license_info.get("expires_at", "")
    
    try:
        expires_at = datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
        current_time = datetime.now()
        
        if current_time > expires_at:
            return False, "License已过期", expires_at_str
        
        return True, "License有效", expires_at_str
        
    except ValueError as e:
        return False, f"日期格式错误: {str(e)}", expires_at_str


@app.get("/")
async def root():
    """根路径 - 服务信息"""
    return {
        "service": "License Server",
        "version": "1.0.0",
        "description": "极简的授权API服务器",
        "endpoints": {
            "verify": "POST /verify - 验证license",
            "health": "GET /health - 健康检查",
            "docs": "GET /docs - API文档"
        }
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "data_file_exists": DATA_FILE.exists()
    }


@app.post("/verify", response_model=LicenseResponse)
async def verify_license(request: LicenseRequest):
    """
    验证license密钥
    
    请求体:
    {
        "license_key": "your-license-key"
    }
    
    响应:
    {
        "status": "valid" | "invalid",
        "message": "详细信息",
        "expires_at": "过期时间(ISO格式)"
    }
    """
    try:
        # 加载license数据
        license_data = load_license_data()
        
        # 验证license
        is_valid, message, expires_at = is_license_valid(request.license_key, license_data)
        
        if is_valid:
            return LicenseResponse(
                status="valid",
                message=message,
                expires_at=expires_at
            )
        else:
            return LicenseResponse(
                status="invalid",
                message=message,
                expires_at=expires_at
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """404错误处理"""
    return JSONResponse(
        status_code=404,
        content={
            "status": "error",
            "message": "API端点不存在",
            "available_endpoints": ["/", "/health", "/verify", "/docs"]
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    # 开发环境运行
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
