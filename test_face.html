<!DOCTYPE html>
<html>
<head>
    <title>Create Test Face Image</title>
</head>
<body>
    <canvas id="canvas" width="400" height="400"></canvas>
    <br>
    <button onclick="downloadImage()">Download Test Image</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制一个简单的人脸
        ctx.fillStyle = '#ffdbac';
        ctx.fillRect(0, 0, 400, 400);
        
        // 脸部轮廓
        ctx.fillStyle = '#ffdbac';
        ctx.beginPath();
        ctx.ellipse(200, 200, 120, 150, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // 眼睛
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.ellipse(170, 170, 15, 20, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.ellipse(230, 170, 15, 20, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // 鼻子
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(200, 190);
        ctx.lineTo(195, 210);
        ctx.lineTo(205, 210);
        ctx.stroke();
        
        // 嘴巴
        ctx.beginPath();
        ctx.arc(200, 240, 20, 0, Math.PI);
        ctx.stroke();
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'test_face.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
