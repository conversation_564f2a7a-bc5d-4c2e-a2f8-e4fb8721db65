#!/usr/bin/env python3
"""
交通事故分类算法 - 客户调用示例
使用说明：
1. 确保已安装所需依赖：pip install torch torchvision pillow requests numpy
2. 在 config.ini 中配置您的授权密钥
3. 运行此脚本进行推理测试
"""

import os
import sys
import configparser
import argparse
from PIL import Image, ImageDraw, ImageFont
import time
import json
from pathlib import Path

# 导入推理引擎
try:
    from inference_engine import AccidentClassificationEngine
except ImportError as e:
    print(f"错误：无法导入推理引擎模块: {e}")
    print("请确保 inference_engine.py 文件在同一目录下")
    sys.exit(1)


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()

    # 获取脚本所在目录的父目录，然后找到配置文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)

    # 优先使用项目根目录下的 config.ini
    config_file = os.path.join(project_root, 'config.ini')

    # 如果不存在，尝试 src/config/dev.ini
    if not os.path.exists(config_file):
        alt_config = os.path.join(script_dir, 'config', 'dev.ini')
        if os.path.exists(alt_config):
            config_file = alt_config
        else:
            print(f"错误：配置文件不存在，已尝试以下位置:")
            print(f"  - {config_file}")
            print(f"  - {alt_config}")
            print("请创建配置文件并填入您的授权密钥")
            return None, None

    if not os.path.exists(config_file):
        print(f"错误：配置文件 {config_file} 不存在")
        print("请创建配置文件并填入您的授权密钥")
        return None, None

    try:
        config.read(config_file, encoding='utf-8')
        license_key = config['LICENSE']['key']
        model_path = config.get('MODEL', 'model_path', fallback='models/model_weights.pth')

        if not license_key or license_key == 'YOUR_UNIQUE_LICENSE_KEY_FOR_THIS_CLIENT':
            print(f"错误：请在 {config_file} 中配置有效的授权密钥")
            return None, None

        return license_key, model_path
    except Exception as e:
        print(f"错误：读取配置文件失败: {e}")
        return None, None


def save_classification_result(image_path, result, output_dir=None):
    """保存分类结果"""
    # 自动根据环境切换路径
    if output_dir is None:
        if os.getenv("IN_DOCKER"):
            output_dir = "/app/data/output"
        else:
            # 获取项目根目录（accident_classify目录）
            script_dir = Path(__file__).parent
            project_root = script_dir.parent
            output_dir = str(project_root / "data" / "output")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 获取文件名（不含扩展名）
    image_name = Path(image_path).stem

    # 1. 保存 JSON 结果
    json_path = os.path.join(output_dir, f"{image_name}_classification.json")
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    # 2. 创建带分类标签的图像
    try:
        # 打开原图
        image = Image.open(image_path)
        draw = ImageDraw.Draw(image)

        # 尝试使用系统字体，如果失败则使用默认字体
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 40)
        except:
            try:
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()

        # 准备文本
        class_name = result['class_name']
        confidence = result['confidence']
        text = f"{class_name}: {confidence:.3f}"

        # 选择颜色（事故用红色，正常用绿色）
        color = (255, 0, 0) if class_name == 'accident' else (0, 255, 0)

        # 获取文本尺寸
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # 在图像顶部绘制背景矩形
        padding = 10
        rect_coords = [
            (padding, padding),
            (text_width + padding * 2, text_height + padding * 2)
        ]
        draw.rectangle(rect_coords, fill=(0, 0, 0, 128))

        # 绘制文本
        draw.text((padding, padding), text, fill=color, font=font)

        # 保存带标签的图像
        output_image_path = os.path.join(output_dir, f"{image_name}_classified.jpg")
        image.save(output_image_path, "JPEG", quality=95)

        return json_path, output_image_path

    except Exception as e:
        print(f"保存可视化结果时出错: {e}")
        return json_path, None


def process_single_image(engine, image_path, save_result=True, output_dir=None):
    """处理单张图像"""
    print(f"\n正在处理图像: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"错误：图像文件不存在: {image_path}")
        return
    
    try:
        start_time = time.time()
        result = engine.predict(image_path)
        end_time = time.time()
        
        print(f"预测结果:")
        print(f"  类别: {result['class_name']}")
        print(f"  置信度: {result['confidence']:.4f}")
        print(f"  处理时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"  详细概率:")
        for class_name, prob in result['probabilities'].items():
            print(f"    {class_name}: {prob:.4f}")

        # 保存结果
        if save_result:
            json_path, image_path = save_classification_result(image_path, result, output_dir)
            print(f"\n结果已保存:")
            print(f"  JSON结果: {json_path}")
            if image_path:
                print(f"  可视化结果: {image_path}")

    except Exception as e:
        print(f"预测失败: {e}")


def process_video(engine, video_path, save_result=False, output_dir=None, frame_interval=10):
    """处理视频文件

    Args:
        engine: 推理引擎
        video_path: 视频文件路径
        save_result: 是否保存结果视频
        output_dir: 输出目录
        frame_interval: 帧间隔，每N帧处理一次（默认10）
    """
    import cv2

    if not os.path.exists(video_path):
        print(f"错误：视频文件不存在: {video_path}")
        return

    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误：无法打开视频文件: {video_path}")
        return

    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"\n处理视频: {video_path}")
    print(f"分辨率: {width}x{height}, 帧率: {fps}, 总帧数: {total_frames}")
    print(f"帧间隔: 每 {frame_interval} 帧处理一次")

    # 初始化输出视频（如果需要保存）
    out = None
    if save_result and output_dir:
        os.makedirs(output_dir, exist_ok=True)
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        output_path = os.path.join(output_dir, f"{video_name}_classified.mp4")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    frame_count = 0
    processed_count = 0
    accident_frames = 0
    normal_frames = 0
    total_time = 0

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame_count += 1

            # 根据设置的帧间隔处理
            if frame_count % frame_interval == 0:
                processed_count += 1

                # 保存当前帧为临时图像
                temp_image_path = "temp_frame.jpg"
                cv2.imwrite(temp_image_path, frame)

                try:
                    # 进行分类
                    start_time = time.time()
                    result = engine.predict(temp_image_path)
                    end_time = time.time()

                    process_time = (end_time - start_time) * 1000
                    total_time += process_time

                    # 统计结果
                    if result['class_name'] == 'accident':
                        accident_frames += 1
                    else:
                        normal_frames += 1

                    # 在帧上添加分类结果
                    if out is not None:
                        # 添加文本标注
                        text = f"{result['class_name']}: {result['confidence']:.2f}"
                        color = (0, 0, 255) if result['class_name'] == 'accident' else (0, 255, 0)
                        cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)

                    print(f"帧 {frame_count}/{total_frames}: {result['class_name']} ({result['confidence']:.2f}) (时间: {process_time:.1f}ms)")

                except Exception as e:
                    print(f"帧 {frame_count} 处理失败: {e}")

                # 清理临时文件
                if os.path.exists(temp_image_path):
                    os.remove(temp_image_path)

            # 写入输出视频
            if out is not None:
                out.write(frame)

    finally:
        cap.release()
        if out is not None:
            out.release()
            print(f"结果视频已保存: {output_path}")

    # 输出统计信息
    if processed_count > 0:
        avg_time = total_time / processed_count
        accident_ratio = accident_frames / processed_count * 100
        print(f"\n处理完成:")
        print(f"  总帧数: {total_frames}")
        print(f"  处理帧数: {processed_count}")
        print(f"  事故帧数: {accident_frames} ({accident_ratio:.1f}%)")
        print(f"  正常帧数: {normal_frames} ({100-accident_ratio:.1f}%)")
        print(f"  平均处理时间: {avg_time:.2f}ms/帧")


def process_batch_images(engine, image_dir):
    """处理批量图像"""
    if not os.path.exists(image_dir):
        print(f"错误：图像目录不存在: {image_dir}")
        return
    
    # 支持的图像格式
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
    
    # 获取所有图像文件
    image_files = []
    for file in os.listdir(image_dir):
        if file.lower().endswith(supported_formats):
            image_files.append(os.path.join(image_dir, file))
    
    if not image_files:
        print(f"在目录 {image_dir} 中未找到支持的图像文件")
        return
    
    print(f"\n开始批量处理 {len(image_files)} 张图像...")
    
    accident_count = 0
    normal_count = 0
    total_time = 0
    
    for i, image_path in enumerate(image_files, 1):
        try:
            start_time = time.time()
            result = engine.predict(image_path)
            end_time = time.time()
            
            processing_time = end_time - start_time
            total_time += processing_time
            
            if result['class_name'] == 'accident':
                accident_count += 1
            else:
                normal_count += 1
            
            print(f"[{i}/{len(image_files)}] {os.path.basename(image_path)}: "
                  f"{result['class_name']} (置信度: {result['confidence']:.3f}, "
                  f"时间: {processing_time*1000:.1f}ms)")
                  
        except Exception as e:
            print(f"[{i}/{len(image_files)}] {os.path.basename(image_path)}: 处理失败 - {e}")
    
    print(f"\n批量处理完成:")
    print(f"  总图像数: {len(image_files)}")
    print(f"  事故图像: {accident_count}")
    print(f"  正常图像: {normal_count}")
    print(f"  平均处理时间: {(total_time/len(image_files))*1000:.2f}ms")


def interactive_mode(engine):
    """交互模式"""
    print("\n=== 交互模式 ===")
    print("输入图像路径进行预测，输入 'quit' 退出")
    
    while True:
        try:
            image_path = input("\n请输入图像路径: ").strip()
            
            if image_path.lower() in ['quit', 'exit', 'q']:
                print("退出交互模式")
                break
            
            if not image_path:
                continue
                
            process_single_image(engine, image_path, save_result=True)
            
        except KeyboardInterrupt:
            print("\n\n用户中断，退出交互模式")
            break
        except Exception as e:
            print(f"发生错误: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("交通事故分类算法 - 推理测试程序")
    print("=" * 60)
    
    # 加载配置
    license_key, model_path = load_config()
    if not license_key:
        return

    # 初始化推理引擎
    try:
        print("正在初始化推理引擎...")
        engine = AccidentClassificationEngine(license_key=license_key, model_path=model_path)
        print("✓ 推理引擎初始化成功")
    except Exception as e:
        print(f"✗ 推理引擎初始化失败: {e}")
        return
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='交通事故分类算法推理程序')
    parser.add_argument('input', nargs='?', help='输入文件或目录路径')
    parser.add_argument('--video', action='store_true', help='强制作为视频处理')
    parser.add_argument('--frame-interval', type=int, default=10,
                       help='视频处理帧间隔，每N帧处理一次 (默认: 10)')
    parser.add_argument('--real-time', action='store_true',
                       help='实时处理模式 (等同于 --frame-interval 1)')
    parser.add_argument('--output-video', action='store_true',
                       help='输出检测结果视频')
    parser.add_argument('--no-save', action='store_true',
                       help='不保存结果文件')

    args = parser.parse_args()

    # 处理实时模式
    if args.real_time:
        args.frame_interval = 1

    # 自动根据环境切换路径
    if os.getenv("IN_DOCKER"):
        output_dir = "/app/data/output"
    else:
        # 获取项目根目录（accident_classify目录）
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
        output_dir = str(project_root / "data" / "output")

    # 检查输入参数
    if args.input:
        input_path = args.input

        if os.path.isfile(input_path):
            # 检查是否为视频文件
            video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv')
            is_video = args.video or input_path.lower().endswith(video_extensions)

            if is_video:
                # 视频默认保存结果，除非用户明确指定不保存
                save_result = not args.no_save
                process_video(engine, input_path, save_result=save_result,
                          output_dir=output_dir, frame_interval=args.frame_interval)
            else:
                save_result = not args.no_save
                process_single_image(engine, input_path, save_result=save_result, output_dir=output_dir)
        elif os.path.isdir(input_path):
            process_batch_images(engine, input_path)
        else:
            print(f"错误：路径不存在: {input_path}")
    else:
        # 交互模式
        interactive_mode(engine)


if __name__ == "__main__":
    main()
