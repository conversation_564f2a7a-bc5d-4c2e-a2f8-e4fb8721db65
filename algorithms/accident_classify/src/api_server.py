#!/usr/bin/env python3
"""
交通事故分类算法 - API服务器
提供RESTful API接口用于交通事故类型分类
"""

import os
import io
import time
from typing import List, Optional
import configparser

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image

# 导入推理引擎
from inference_engine import AccidentClassificationEngine
from logger_config import get_logger

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="交通事故分类算法API",
    description="基于深度学习的交通事故类型分类API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[AccidentClassificationEngine] = None


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[dict] = None
    error: Optional[str] = None
    timestamp: str
    processing_time: Optional[float] = None


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_file = "config.ini"
    
    try:
        config.read(config_file, encoding='utf-8')
        conf_threshold = config.getfloat('CLASSIFICATION', 'confidence_threshold', fallback=0.5)
        batch_size = config.getint('CLASSIFICATION', 'batch_size', fallback=8)
        return conf_threshold, batch_size
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return 0.5, 8


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化交通事故分类引擎...")
        config_path = "config.ini"
        engine = AccidentClassificationEngine(config_path)
        logger.info("交通事故分类引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health")
async def health_check():
    """健康检查接口"""
    return APIResponse(
        success=True,
        message="服务运行正常",
        data={
            "service": "交通事故分类算法",
            "status": "healthy",
            "engine_loaded": engine is not None
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.get("/api/v1/info")
async def get_algorithm_info():
    """获取算法信息"""
    return APIResponse(
        success=True,
        message="算法信息获取成功",
        data={
            "name": "交通事故分类算法",
            "version": "1.0.0",
            "description": "基于深度学习的交通事故类型分类算法",
            "capabilities": [
                "事故类型分类",
                "严重程度评估",
                "置信度评分"
            ],
            "classes": [
                "轻微事故",
                "一般事故", 
                "重大事故",
                "特大事故",
                "非事故"
            ],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"]
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.post("/api/v1/classify")
async def classify_accident(
    file: UploadFile = File(...),
    return_all_scores: bool = Form(default=True)
):
    """事故分类接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    
    try:
        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为numpy数组
        image_array = np.array(image)
        
        # 执行推理
        results = engine.predict(image_array, return_all_scores=return_all_scores)
        
        processing_time = time.time() - start_time
        
        return APIResponse(
            success=True,
            message=f"分类完成，预测类别: {results['predicted_class']}",
            data=results,
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"事故分类失败: {e}")
        return APIResponse(
            success=False,
            message="事故分类失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/classify_batch")
async def classify_batch(files: List[UploadFile] = File(...)):
    """批量分类接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    results = []
    
    try:
        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_array = np.array(image)
                
                # 执行推理
                result = engine.predict(image_array, return_all_scores=True)
                result['filename'] = file.filename
                result['index'] = i
                results.append(result)
                
            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'predicted_class': 'unknown',
                    'confidence': 0.0
                })
        
        processing_time = time.time() - start_time
        
        # 统计结果
        class_counts = {}
        for result in results:
            if 'error' not in result:
                class_name = result['predicted_class']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        return APIResponse(
            success=True,
            message=f"批量分类完成，处理 {len(files)} 张图像",
            data={
                "total_images": len(files),
                "class_distribution": class_counts,
                "results": results
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"批量分类失败: {e}")
        return APIResponse(
            success=False,
            message="批量分类失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


if __name__ == "__main__":
    # 启动API服务器
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8003,
        reload=False,
        log_level="info"
    )
