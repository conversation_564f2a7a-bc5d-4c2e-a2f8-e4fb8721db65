"""
交通事故分类推理引擎
包含授权验证逻辑的核心推理模块
注意：此文件在交付前需要使用 pyarmor 进行代码混淆处理
"""

import torch
import torch.nn as nn
import numpy as np
import requests
import hashlib
import time
from datetime import datetime, timedelta
from PIL import Image
import torchvision.transforms as transforms
import os
import json


class Bottleneck(nn.Module):
    """ResNet50 Bottleneck Block"""
    expansion = 4

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None):
        super(Bottleneck, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        width = int(planes * (base_width / 64.)) * groups
        
        self.conv1 = nn.Conv2d(inplanes, width, kernel_size=1, bias=False)
        self.bn1 = norm_layer(width)
        self.conv2 = nn.Conv2d(width, width, kernel_size=3, stride=stride,
                               padding=dilation, groups=groups, bias=False, dilation=dilation)
        self.bn2 = norm_layer(width)
        self.conv3 = nn.Conv2d(width, planes * self.expansion, kernel_size=1, bias=False)
        self.bn3 = norm_layer(planes * self.expansion)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        identity = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)
        
        out = self.conv3(out)
        out = self.bn3(out)
        
        if self.downsample is not None:
            identity = self.downsample(x)
            
        out += identity
        out = self.relu(out)
        
        return out


class ResNet50AccidentClassifier(nn.Module):
    """交通事故分类ResNet50模型"""
    
    def __init__(self, num_classes=2):
        super(ResNet50AccidentClassifier, self).__init__()
        self.inplanes = 64
        self.dilation = 1
        self.groups = 1
        self.base_width = 64
        
        # 输入层
        self.conv1 = nn.Conv2d(3, self.inplanes, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm2d(self.inplanes)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        
        # ResNet层
        self.layer1 = self._make_layer(Bottleneck, 64, 3)
        self.layer2 = self._make_layer(Bottleneck, 128, 4, stride=2)
        self.layer3 = self._make_layer(Bottleneck, 256, 6, stride=2)
        self.layer4 = self._make_layer(Bottleneck, 512, 3, stride=2)
        
        # 分类头
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * Bottleneck.expansion, num_classes)
        
    def _make_layer(self, block, planes, blocks, stride=1, dilate=False):
        norm_layer = nn.BatchNorm2d
        downsample = None
        previous_dilation = self.dilation
        
        if dilate:
            self.dilation *= stride
            stride = 1
            
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv2d(self.inplanes, planes * block.expansion, kernel_size=1, stride=stride, bias=False),
                norm_layer(planes * block.expansion),
            )
            
        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample, self.groups,
                           self.base_width, previous_dilation, norm_layer))
        self.inplanes = planes * block.expansion
        
        for _ in range(1, blocks):
            layers.append(block(self.inplanes, planes, groups=self.groups,
                               base_width=self.base_width, dilation=self.dilation,
                               norm_layer=norm_layer))
                               
        return nn.Sequential(*layers)
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        
        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)
        
        return x


# 授权验证已移除，算法可直接使用
        
    def _verify_license_remote(self):
        """远程验证授权"""
        try:
            payload = {
                "license_key": self.license_key
            }

            response = requests.post(
                self.license_server_url,
                json=payload,
                timeout=10,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "valid":
                    self.last_check_time = datetime.now()
                    return True, data.get("message", "授权验证成功")
                else:
                    return False, data.get("message", "授权无效")
            else:
                return False, f"服务器响应错误: {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "授权服务器连接超时，请检查网络连接"
        except requests.exceptions.ConnectionError:
            return False, "无法连接到授权服务器，请检查网络连接"
        except Exception as e:
            return False, f"授权验证失败: {str(e)}"
    
    def verify_license(self, force_check=False):
        """验证授权"""
        current_time = datetime.now()
        
        # 如果是强制检查或者超过检查间隔，则进行远程验证
        if (force_check or 
            self.last_check_time is None or 
            (current_time - self.last_check_time).seconds > self.check_interval):
            
            is_valid, message = self._verify_license_remote()
            if not is_valid:
                raise Exception(f"授权验证失败: {message}")
            return True
        
        return True


class AccidentClassificationEngine:
    """交通事故分类推理引擎"""
    
    def __init__(self, license_key, model_path="models/model_weights.pth"):
        self.license_manager = LicenseManager(license_key)
        self.model = None

        # 如果是相对路径，转换为相对于项目根目录的路径
        if not os.path.isabs(model_path):
            # 获取当前脚本所在目录的父目录（项目根目录）
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(script_dir)
            self.model_path = os.path.join(project_root, model_path)
        else:
            self.model_path = model_path
        self.class_names = ['normal', 'accident']
        self.input_size = (224, 224)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize(self.input_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self._initialize()
    
    def _initialize(self):
        """初始化引擎"""
        print("正在验证授权...")
        
        # 验证授权
        try:
            self.license_manager.verify_license(force_check=True)
            print("✓ 授权验证通过")
        except Exception as e:
            raise Exception(f"授权验证失败: {e}")
        
        # 加载模型
        print("正在加载模型...")
        try:
            self._load_model()
            print("✓ 模型加载成功")
        except Exception as e:
            raise Exception(f"模型加载失败: {e}")
    
    def _load_model(self):
        """加载模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        # 创建模型
        self.model = ResNet50AccidentClassifier(num_classes=len(self.class_names))
        
        # 加载权重
        state_dict = torch.load(self.model_path, map_location=self.device)
        self.model.load_state_dict(state_dict)
        
        # 设置为评估模式
        self.model.eval()
        self.model.to(self.device)
    
    def _preprocess_image(self, image):
        """图像预处理"""
        if isinstance(image, str):
            # 如果是文件路径
            image = Image.open(image).convert('RGB')
        elif isinstance(image, np.ndarray):
            # 如果是numpy数组
            image = Image.fromarray(image).convert('RGB')
        elif not isinstance(image, Image.Image):
            raise ValueError("输入必须是PIL Image、numpy数组或图像文件路径")
        
        # 应用预处理
        image_tensor = self.transform(image).unsqueeze(0)
        return image_tensor.to(self.device)
    
    def predict(self, image):
        """执行推理预测"""
        # 定期检查授权
        try:
            self.license_manager.verify_license()
        except Exception as e:
            raise Exception(f"授权检查失败: {e}")
        
        if self.model is None:
            raise Exception("模型未加载，请先完成初始化")
        
        # 预处理图像
        image_tensor = self._preprocess_image(image)
        
        # 执行推理
        with torch.no_grad():
            outputs = self.model(image_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            predicted_class_idx = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0][predicted_class_idx].item()
        
        result = {
            'class_name': self.class_names[predicted_class_idx],
            'class_index': predicted_class_idx,
            'confidence': confidence,
            'probabilities': {
                self.class_names[i]: probabilities[0][i].item() 
                for i in range(len(self.class_names))
            }
        }
        
        return result
    
    def batch_predict(self, images):
        """批量预测"""
        results = []
        for image in images:
            try:
                result = self.predict(image)
                results.append(result)
            except Exception as e:
                results.append({'error': str(e)})
        return results
