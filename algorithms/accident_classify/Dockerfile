# 交通事故分类算法 - 统一Docker镜像
# 支持开发和生产环境
FROM python:3.9-slim

# 构建参数
ARG ENV=prod
ENV ENVIRONMENT=${ENV}
ENV IN_DOCKER=1
ENV UV_SYSTEM_PYTHON=1
ENV PYTHONPATH=/app

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install --no-cache-dir uv

# 复制项目配置文件和README
COPY pyproject.toml uv.lock README.md ./

# 安装Python依赖
RUN uv sync --frozen --no-dev

# 创建必要的目录
RUN mkdir -p /app/data/input /app/data/output /app/logs

# 复制源代码
COPY src/ ./src/

# 复制模型文件
COPY models/ ./models/

# 根据环境选择配置文件
RUN cp src/config/${ENVIRONMENT}.ini ./config.ini

# 设置权限
RUN chmod +x src/run_inference.py

# 创建非root用户
RUN useradd -m -u 1000 appuser && \
    chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD uv run python -c "import sys; sys.path.append('src'); import inference_engine; print('OK')" || exit 1

# 默认命令
CMD ["uv", "run", "python", "src/run_inference.py", "--help"]
