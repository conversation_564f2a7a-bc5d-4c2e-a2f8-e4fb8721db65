#!/bin/bash
# 事故分类算法 - 一键部署脚本
# 独立脚本，不依赖外部文件

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALGORITHM_ROOT="$(dirname "$SCRIPT_DIR")"
CONTAINER_NAME="accident-classify"
IMAGE_NAME="accident-classify:latest"
NETWORK_NAME="accident-classify-network"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示用法
show_usage() {
    echo "事故分类算法 - 部署脚本"
    echo "用法: $0 [options]"
    echo ""
    echo "选项:"
    echo "  --rebuild        重新构建镜像后部署"
    echo "  --stop           停止容器"
    echo "  --restart        重启容器"
    echo "  --logs           查看容器日志"
    echo "  --status         查看容器状态"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动容器"
    echo "  $0 --rebuild          # 重新构建并部署"
    echo "  $0 --stop             # 停止容器"
    echo "  $0 --logs             # 查看日志"
}

# 停止并删除容器
stop_container() {
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        print_info "停止容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME"
    fi
    
    if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
        print_info "删除容器: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
    fi
}

# 启动容器
start_container() {
    print_info "启动容器: $CONTAINER_NAME"
    
    # 确保网络存在
    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        print_info "创建 Docker 网络: $NETWORK_NAME"
        docker network create "$NETWORK_NAME"
    fi
    
    # 启动容器
    docker run -d --name "$CONTAINER_NAME" \
        -v "$ALGORITHM_ROOT/data/input:/app/data/input" \
        -v "$ALGORITHM_ROOT/data/output:/app/data/output" \
        -v "$ALGORITHM_ROOT/logs:/app/logs" \
        --network "$NETWORK_NAME" \
        --add-host host.docker.internal:host-gateway \
        "$IMAGE_NAME" tail -f /dev/null
    
    print_success "容器启动成功: $CONTAINER_NAME"
    
    # 等待容器完全启动
    sleep 2
    
    # 测试容器
    print_info "测试容器功能..."
    if docker exec "$CONTAINER_NAME" python -c "print('Container is ready!')"; then
        print_success "容器测试通过"
    else
        print_error "容器测试失败"
        return 1
    fi
}

# 查看日志
show_logs() {
    print_info "显示容器日志: $CONTAINER_NAME"
    docker logs -f "$CONTAINER_NAME"
}

# 重启容器
restart_container() {
    print_info "重启容器: $CONTAINER_NAME"
    stop_container
    start_container
}

# 查看状态
show_status() {
    print_info "容器状态:"
    if docker ps | grep -q "$CONTAINER_NAME"; then
        docker ps | grep "$CONTAINER_NAME"
        print_success "容器正在运行"
    else
        print_warning "容器未运行"
    fi
    
    print_info "镜像信息:"
    docker images | grep "accident-classify" || print_warning "镜像不存在"
}

# 主函数
main() {
    cd "$ALGORITHM_ROOT"
    
    local action="start"
    local rebuild=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --rebuild)
                rebuild=true
                shift
                ;;
            --stop)
                action="stop"
                shift
                ;;
            --restart)
                action="restart"
                shift
                ;;
            --logs)
                action="logs"
                shift
                ;;
            --status)
                action="status"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_info "开始部署事故分类算法"
    print_info "算法根目录: $ALGORITHM_ROOT"
    print_info "操作: $action"
    print_info "容器名称: $CONTAINER_NAME"
    print_info "镜像名称: $IMAGE_NAME"
    
    # 检查 Docker 是否可用
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装或不可用"
        exit 1
    fi
    
    # 如果需要重新构建
    if [ "$rebuild" = true ]; then
        print_info "重新构建镜像..."
        if ! "$SCRIPT_DIR/build_unified.sh"; then
            print_error "镜像构建失败"
            exit 1
        fi
    fi
    
    # 执行操作
    case "$action" in
        "start")
            stop_container
            start_container
            ;;
        "stop")
            stop_container
            print_success "容器已停止: $CONTAINER_NAME"
            ;;
        "restart")
            restart_container
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        *)
            print_error "未知操作: $action"
            exit 1
            ;;
    esac
    
    if [ "$action" != "logs" ] && [ "$action" != "status" ]; then
        print_success "部署操作完成！"
        
        # 显示运行状态
        print_info "当前运行状态:"
        show_status
    fi
}

main "$@"
