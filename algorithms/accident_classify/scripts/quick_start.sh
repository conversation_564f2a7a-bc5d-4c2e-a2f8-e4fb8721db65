#!/bin/bash
# 事故分类算法 - 快速开始脚本
# 独立脚本，提供交互式菜单

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALGORITHM_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示菜单
show_menu() {
    print_header "事故分类算法 - 快速开始"
    echo ""
    echo "请选择操作:"
    echo ""
    echo "📱 本地开发:"
    echo "  1) 运行本地推理"
    echo "  2) 查看本地日志"
    echo ""
    echo "🐳 Docker 部署:"
    echo "  3) 运行 Docker 推理"
    echo "  4) 构建 Docker 镜像"
    echo "  5) 部署 Docker 容器"
    echo "  6) 一键重新部署"
    echo ""
    echo "📊 系统管理:"
    echo "  7) 查看容器状态"
    echo "  8) 查看容器日志"
    echo "  9) 停止容器"
    echo "  10) 重启容器"
    echo ""
    echo "  0) 退出"
    echo ""
}

# 本地运行
run_local() {
    print_info "运行本地事故分类算法..."

    if [ ! -d "$ALGORITHM_ROOT/src" ]; then
        print_error "源代码目录不存在: $ALGORITHM_ROOT/src"
        return 1
    fi

    cd "$ALGORITHM_ROOT"

    # 让用户选择输入方式
    echo ""
    echo "请选择输入方式:"
    echo "  1) 手动输入图像文件路径"
    echo "  2) 手动输入视频文件路径"
    echo "  3) 使用默认测试图像"
    echo "  4) 批量处理目录"
    echo ""
    read -p "请选择 [1-4]: " input_choice

    local input_file=""

    case $input_choice in
        1)
            echo ""
            print_info "请输入图像文件的完整路径:"
            print_info "支持格式: jpg, jpeg, png"
            print_info "示例: /path/to/your/image.jpg"
            print_info "或相对路径: data/input/your_image.jpg"
            echo ""
            read -p "图像路径: " input_file

            # 检查文件是否存在
            if [ ! -f "$input_file" ]; then
                print_error "文件不存在: $input_file"
                return 1
            fi

            # 检查文件格式
            input_file_lower=$(echo "$input_file" | tr '[:upper:]' '[:lower:]')
            case "$input_file_lower" in
                *.jpg|*.jpeg|*.png)
                    print_success "图像文件格式有效: $input_file"
                    ;;
                *)
                    print_error "不支持的图像格式，请使用 jpg, jpeg 或 png 格式"
                    return 1
                    ;;
            esac
            ;;
        2)
            echo ""
            print_info "请输入视频文件的完整路径:"
            print_info "支持格式: mp4, avi, mov, mkv, flv, wmv"
            print_info "示例: /path/to/your/video.mp4"
            print_info "或相对路径: data/input/your_video.mp4"
            echo ""
            read -p "视频路径: " input_file

            # 检查文件是否存在
            if [ ! -f "$input_file" ]; then
                print_error "文件不存在: $input_file"
                return 1
            fi

            # 检查文件格式
            input_file_lower=$(echo "$input_file" | tr '[:upper:]' '[:lower:]')
            case "$input_file_lower" in
                *.mp4|*.avi|*.mov|*.mkv|*.flv|*.wmv)
                    print_success "视频文件格式有效: $input_file"
                    ;;
                *)
                    print_error "不支持的视频格式，请使用 mp4, avi, mov, mkv, flv 或 wmv 格式"
                    return 1
                    ;;
            esac

            # 询问视频处理选项
            echo ""
            echo "请选择视频处理方式:"
            echo "  1) 实时检测 (逐帧处理，速度较慢)"
            echo "  2) 关键帧检测 (每N帧处理一次，推荐)"
            echo "  3) 快速检测 (每5帧处理一次，生成标注视频)"
            echo ""
            echo "注意：所有模式都会自动保存检测结果视频到 data/output/ 目录"
            echo ""
            read -p "请选择 [1-3]: " video_mode

            case $video_mode in
                1)
                    print_info "选择实时检测模式"
                    ;;
                2)
                    echo ""
                    read -p "请输入帧间隔 (默认5，即每5帧处理一次): " frame_interval
                    frame_interval=${frame_interval:-5}
                    print_info "选择关键帧检测模式，帧间隔: $frame_interval"
                    ;;
                3)
                    print_info "选择输出检测视频模式"
                    ;;
                *)
                    print_warning "无效选择，使用默认实时检测模式"
                    video_mode=1
                    ;;
            esac
            ;;
        3)
            # 查找默认测试图像
            print_info "查找默认测试图像..."
            for img in "data/input"/*.{jpg,png,jpeg}; do
                if [ -f "$img" ]; then
                    input_file="$img"
                    break
                fi
            done

            if [ -z "$input_file" ]; then
                print_error "未找到默认测试图像"
                print_info "请将测试图像放入 data/input/ 目录"
                return 1
            fi

            print_success "找到默认测试图像: $input_file"
            ;;
        4)
            echo ""
            print_info "请输入要批量处理的目录路径:"
            print_info "示例: /path/to/images/ 或 data/input/"
            echo ""
            read -p "目录路径: " input_dir

            # 检查目录是否存在
            if [ ! -d "$input_dir" ]; then
                print_error "目录不存在: $input_dir"
                return 1
            fi

            # 检查目录中是否有图像或视频文件
            image_count=$(find "$input_dir" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \) | wc -l)
            video_count=$(find "$input_dir" -type f \( -iname "*.mp4" -o -iname "*.avi" -o -iname "*.mov" -o -iname "*.mkv" -o -iname "*.flv" -o -iname "*.wmv" \) | wc -l)
            total_count=$((image_count + video_count))

            if [ "$total_count" -eq 0 ]; then
                print_error "目录中没有找到图像或视频文件: $input_dir"
                return 1
            fi

            print_success "找到 $image_count 个图像文件和 $video_count 个视频文件"
            input_file="$input_dir"
            ;;
        *)
            print_error "无效选择"
            return 1
            ;;
    esac

    echo ""
    print_info "开始执行推理..."
    print_info "输入: $input_file"

    # 执行推理
    if command -v uv &> /dev/null; then
        if [ "$input_choice" = "2" ]; then
            # 视频处理
            case $video_mode in
                1)
                    print_info "开始实时视频检测..."
                    uv run python src/run_inference.py "$input_file" --video --real-time
                    ;;
                2)
                    print_info "开始关键帧视频检测 (间隔: $frame_interval 帧)..."
                    uv run python src/run_inference.py "$input_file" --video --frame-interval "$frame_interval"
                    ;;
                3)
                    print_info "开始生成检测视频..."
                    uv run python src/run_inference.py "$input_file" --video --output-video --frame-interval 5
                    ;;
            esac
        elif [ "$input_choice" = "4" ]; then
            # 批量处理
            print_info "批量处理图像文件..."
            find "$input_file" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \) -exec uv run python src/run_inference.py {} \;

            print_info "批量处理视频文件..."
            find "$input_file" -type f \( -iname "*.mp4" -o -iname "*.avi" -o -iname "*.mov" -o -iname "*.mkv" -o -iname "*.flv" -o -iname "*.wmv" \) -exec uv run python src/run_inference.py {} --video \;
        else
            # 单个图像文件
            uv run python src/run_inference.py "$input_file"
        fi
    else
        if [ "$input_choice" = "2" ]; then
            # 视频处理
            case $video_mode in
                1)
                    print_info "开始实时视频检测..."
                    python src/run_inference.py "$input_file" --video --real-time
                    ;;
                2)
                    print_info "开始关键帧视频检测 (间隔: $frame_interval 帧)..."
                    python src/run_inference.py "$input_file" --video --frame-interval "$frame_interval"
                    ;;
                3)
                    print_info "开始生成检测视频..."
                    python src/run_inference.py "$input_file" --video --output-video --frame-interval 5
                    ;;
            esac
        elif [ "$input_choice" = "4" ]; then
            # 批量处理
            print_info "批量处理图像文件..."
            find "$input_file" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \) -exec python src/run_inference.py {} \;

            print_info "批量处理视频文件..."
            find "$input_file" -type f \( -iname "*.mp4" -o -iname "*.avi" -o -iname "*.mov" -o -iname "*.mkv" -o -iname "*.flv" -o -iname "*.wmv" \) -exec python src/run_inference.py {} --video \;
        else
            # 单个图像文件
            python src/run_inference.py "$input_file"
        fi
    fi

    echo ""
    print_success "推理完成！"

    # 显示输出结果
    if [ -d "data/output" ] && [ "$(ls -A data/output 2>/dev/null)" ]; then
        print_info "结果保存在 data/output/ 目录:"
        ls -la data/output/ | tail -10
    else
        print_warning "输出目录为空，请检查推理是否成功"
    fi
}

# Docker 运行
run_docker() {
    print_info "运行 Docker 事故分类算法..."

    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 未运行，请先启动 Docker"
        return 1
    fi

    # 检查容器是否存在
    local container_name="accident-classifier"
    if ! docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        print_error "容器 ${container_name} 不存在"
        print_info "请先运行: 选项 5) 部署 Docker 容器"
        return 1
    fi

    # 检查容器是否运行
    if ! docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        print_info "启动容器 ${container_name}..."
        docker start "${container_name}"
        sleep 2
    fi

    cd "$ALGORITHM_ROOT"

    # 让用户选择输入方式
    echo ""
    echo "请选择输入方式:"
    echo "  1) 手动输入图像文件路径"
    echo "  2) 手动输入视频文件路径"
    echo "  3) 使用默认测试图像"
    echo "  4) 批量处理目录"
    echo ""
    read -p "请选择 [1-4]: " input_choice

    local input_file=""
    local docker_path=""

    case $input_choice in
        1)
            echo ""
            print_info "请输入图像文件的完整路径:"
            print_info "支持格式: jpg, jpeg, png"
            print_info "示例: /path/to/your/image.jpg"
            print_info "或相对路径: data/input/your_image.jpg"
            echo ""
            read -p "图像路径: " input_file

            # 检查文件是否存在
            if [ ! -f "$input_file" ]; then
                print_error "文件不存在: $input_file"
                return 1
            fi

            # 检查文件格式
            input_file_lower=$(echo "$input_file" | tr '[:upper:]' '[:lower:]')
            case "$input_file_lower" in
                *.jpg|*.jpeg|*.png)
                    print_success "图像文件格式有效: $input_file"
                    ;;
                *)
                    print_error "不支持的图像格式，请使用 jpg, jpeg 或 png 格式"
                    return 1
                    ;;
            esac

            # 转换为容器内路径
            if [[ "$input_file" == /* ]]; then
                # 绝对路径，需要挂载
                docker_path="/tmp/input/$(basename "$input_file")"
                docker cp "$input_file" "${container_name}:/tmp/input/"
            else
                # 相对路径，假设在项目目录内
                docker_path="/app/$input_file"
            fi
            ;;
        2)
            echo ""
            print_info "请输入视频文件的完整路径:"
            print_info "支持格式: mp4, avi, mov, mkv, flv, wmv"
            print_info "示例: /path/to/your/video.mp4"
            print_info "或相对路径: data/input/your_video.mp4"
            echo ""
            read -p "视频路径: " input_file

            # 检查文件是否存在
            if [ ! -f "$input_file" ]; then
                print_error "文件不存在: $input_file"
                return 1
            fi

            # 检查文件格式
            input_file_lower=$(echo "$input_file" | tr '[:upper:]' '[:lower:]')
            case "$input_file_lower" in
                *.mp4|*.avi|*.mov|*.mkv|*.flv|*.wmv)
                    print_success "视频文件格式有效: $input_file"
                    ;;
                *)
                    print_error "不支持的视频格式，请使用 mp4, avi, mov, mkv, flv 或 wmv 格式"
                    return 1
                    ;;
            esac

            # 询问视频处理模式
            echo ""
            echo "请选择视频处理模式:"
            echo "  1) 实时检测 (逐帧处理)"
            echo "  2) 关键帧检测 (间隔处理)"
            echo "  3) 生成检测视频"
            echo ""
            read -p "请选择 [1-3]: " video_mode

            local frame_interval=10
            if [ "$video_mode" = "2" ]; then
                echo ""
                read -p "请输入帧间隔 (默认: 10): " frame_interval
                frame_interval=${frame_interval:-10}
            fi

            # 转换为容器内路径
            if [[ "$input_file" == /* ]]; then
                # 绝对路径，需要挂载
                docker_path="/tmp/input/$(basename "$input_file")"
                docker cp "$input_file" "${container_name}:/tmp/input/"
            else
                # 相对路径，假设在项目目录内
                docker_path="/app/$input_file"
            fi
            ;;
        3)
            # 查找默认测试图像
            print_info "查找默认测试图像..."

            for img in "data/input"/*.{jpg,png,jpeg}; do
                if [ -f "$img" ]; then
                    input_file="$img"
                    break
                fi
            done

            if [ -z "$input_file" ]; then
                print_error "未找到默认测试图像"
                print_info "请将测试图像放入 data/input/ 目录"
                return 1
            fi

            print_success "找到默认测试图像: $input_file"
            docker_path="/app/$input_file"
            ;;
        4)
            echo ""
            print_info "请输入要批量处理的目录路径:"
            print_info "示例: /path/to/images/ 或 data/input/"
            echo ""
            read -p "目录路径: " input_dir

            # 检查目录是否存在
            if [ ! -d "$input_dir" ]; then
                print_error "目录不存在: $input_dir"
                return 1
            fi

            # 检查目录中是否有图像文件
            image_count=$(find "$input_dir" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \) | wc -l)
            if [ "$image_count" -eq 0 ]; then
                print_error "目录中没有找到图像文件: $input_dir"
                return 1
            fi

            print_success "找到 $image_count 个图像文件"
            input_file="$input_dir"

            # 转换为容器内路径
            if [[ "$input_file" == /* ]]; then
                # 绝对路径，需要挂载
                docker_path="/tmp/input/"
                docker cp "$input_file/." "${container_name}:/tmp/input/"
            else
                # 相对路径，假设在项目目录内
                docker_path="/app/$input_file"
            fi
            ;;
        *)
            print_error "无效选择"
            return 1
            ;;
    esac

    echo ""
    print_info "开始执行 Docker 推理..."
    print_info "容器: $container_name"
    print_info "输入: $docker_path"

    # 执行推理
    if [ "$input_choice" = "2" ]; then
        # 视频处理
        case $video_mode in
            1)
                print_info "开始实时视频检测..."
                docker exec "$container_name" python src/run_inference.py "$docker_path" --video --real-time
                ;;
            2)
                print_info "开始关键帧视频检测 (间隔: $frame_interval 帧)..."
                docker exec "$container_name" python src/run_inference.py "$docker_path" --video --frame-interval "$frame_interval"
                ;;
            3)
                print_info "开始生成检测视频..."
                docker exec "$container_name" python src/run_inference.py "$docker_path" --video --output-video --frame-interval 5
                ;;
        esac
    elif [ "$input_choice" = "4" ]; then
        # 批量处理
        print_info "批量处理图像文件..."
        docker exec "$container_name" python src/run_inference.py "$docker_path"
    else
        # 单个图像文件
        docker exec "$container_name" python src/run_inference.py "$docker_path"
    fi

    echo ""
    print_success "Docker 推理完成！"

    # 显示输出结果
    print_info "检查容器内输出结果..."
    docker exec "$container_name" ls -la /app/data/output/ 2>/dev/null || print_warning "无法访问容器输出目录"
}

# 查看本地日志
view_local_logs() {
    print_info "查看本地日志..."
    
    if [ ! -d "$ALGORITHM_ROOT/logs" ]; then
        print_warning "日志目录不存在"
        return 1
    fi
    
    echo "可用的日志文件:"
    ls -la "$ALGORITHM_ROOT/logs/"
    
    echo ""
    echo "最新的详细日志:"
    if [ -f "$ALGORITHM_ROOT/logs/accident_classify_detailed.log" ]; then
        tail -20 "$ALGORITHM_ROOT/logs/accident_classify_detailed.log"
    else
        print_warning "详细日志文件不存在"
    fi
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_error "缺少依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        return 1
    fi
    
    return 0
}

# 主循环
main() {
    cd "$ALGORITHM_ROOT"
    
    while true; do
        show_menu
        read -p "请输入选择 (0-10): " choice
        echo ""
        
        case $choice in
            1)
                run_local
                ;;
            2)
                view_local_logs
                ;;
            3)
                run_docker
                ;;
            4)
                print_info "构建 Docker 镜像..."
                "$SCRIPT_DIR/build_unified.sh"
                ;;
            5)
                print_info "部署 Docker 容器..."
                "$SCRIPT_DIR/deploy.sh"
                ;;
            6)
                print_info "一键重新部署..."
                "$SCRIPT_DIR/deploy.sh" --rebuild
                ;;
            7)
                "$SCRIPT_DIR/deploy.sh" --status
                ;;
            8)
                "$SCRIPT_DIR/deploy.sh" --logs
                ;;
            9)
                "$SCRIPT_DIR/deploy.sh" --stop
                ;;
            10)
                "$SCRIPT_DIR/deploy.sh" --restart
                ;;
            0)
                print_success "再见！"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按 Enter 键继续..."
        echo ""
    done
}

# 启动
print_header "系统初始化"
if check_dependencies; then
    print_success "依赖检查通过"
    main
else
    exit 1
fi
