# 交通事故分类算法

基于深度学习的交通事故分类算法，支持多种事故类型的智能识别和分类。

## 🚀 快速开始

### 本地运行
```bash
# 安装依赖
uv sync

# 单张图像分类
uv run python src/run_inference.py data/input/accident.jpg

# 批量图像分类
uv run python src/run_inference.py data/input/
```

### Docker运行
```bash
# 构建镜像
docker build -t accident-classify:latest .

# 运行容器
docker run -d --name accident-classify \
  -v $(pwd)/data:/app/data \
  accident-classify:latest

# 执行分类
docker exec accident-classify uv run python src/run_inference.py /app/data/input/accident.jpg
```

## 📋 分类类别

支持以下事故类型分类：
- **轻微事故** - 无人员伤亡的轻微碰撞
- **一般事故** - 有轻微人员伤亡
- **重大事故** - 有重大人员伤亡或财产损失
- **特大事故** - 造成重大人员伤亡的严重事故
- **非事故** - 正常交通场景

## ⚙️ 配置说明

配置文件：`config.ini`

```ini
[MODEL]
model_path = models/accident_classify_model.onnx
input_size = 224
num_classes = 5
device = auto

[CLASSIFICATION]
confidence_threshold = 0.5
enable_gpu = true
batch_size = 8

[OUTPUT]
save_results = true
output_format = json
include_confidence = true
```

## 📊 功能特性

- **多类分类**: 支持5种事故类型分类
- **高精度**: 基于深度学习优化模型
- **批量处理**: 支持目录批量分类
- **GPU加速**: 支持CUDA和MPS加速
- **易部署**: Docker容器化，一键部署
- **结果输出**: JSON格式输出，包含置信度

## 🛠️ 开发环境

- Python 3.11+
- ONNX Runtime 1.16+
- OpenCV 4.8+
- NumPy 1.24+

## 📝 模型文件

需要将模型文件 `accident_classify_model.onnx` 放置在 `models/` 目录。

## 🔧 使用示例

### 命令行参数
```bash
# 基本分类
python src/run_inference.py input.jpg

# 批量分类
python src/run_inference.py input_directory/

# 不保存结果
python src/run_inference.py input.jpg --no-save

# 指定输出目录
python src/run_inference.py input.jpg --output-dir /path/to/output
```

### 输出格式
```json
{
  "image_path": "data/input/accident.jpg",
  "predicted_class": "重大事故",
  "confidence": 0.89,
  "all_predictions": {
    "轻微事故": 0.05,
    "一般事故": 0.03,
    "重大事故": 0.89,
    "特大事故": 0.02,
    "非事故": 0.01
  },
  "processing_time": 0.15
}
```

## 🔧 故障排除

### 常见问题
1. **模型加载失败**: 检查 `models/accident_classify_model.onnx` 是否存在
2. **GPU不可用**: 检查ONNX Runtime GPU版本安装
3. **内存不足**: 降低 `batch_size` 参数

### 性能优化
- 使用GPU加速：安装 `onnxruntime-gpu`
- 调整输入尺寸：根据精度需求调整 `input_size`
- 批量处理：增加 `batch_size` 提高吞吐量
