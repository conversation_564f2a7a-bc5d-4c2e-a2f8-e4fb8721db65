[project]
name = "renchefei-detection"
version = "1.0.0"
description = "人车非检测算法 - 基于 YOLOv5 的目标检测系统"
authors = [
    {name = "AI Algorithm Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "Proprietary"}
requires-python = ">=3.11"
keywords = ["yolo", "detection", "computer-vision", "ai"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]

dependencies = [
    # 深度学习框架
    "torch>=1.8.0",
    "torchvision>=0.9.0",
    
    # 图像处理
    "opencv-python>=4.5.0",
    "Pillow>=8.0.0",
    
    # 数据处理
    "numpy>=1.19.0",
    "pandas>=1.3.0",
    
    # 网络请求
    "requests>=2.25.0",
    
    # 配置文件
    "PyYAML>=5.4.0",
    
    # 进度条
    "tqdm>=4.60.0",
    
    # 图像增强
    "matplotlib>=3.3.0",
    "seaborn>=0.11.0",
    
    # 科学计算
    "scipy>=1.7.0",
]

[project.optional-dependencies]
dev = [
    # 开发工具
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "isort>=5.9.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
    
    # 文档生成
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=0.5.0",
    
    # Jupyter 支持
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0",
]

gpu = [
    # GPU 加速 (可选)
    "torch[cuda]>=1.8.0; sys_platform == 'linux'",
]

[project.urls]
Homepage = "https://github.com/company/renchefei-detection"
Documentation = "https://renchefei-detection.readthedocs.io"
Repository = "https://github.com/company/renchefei-detection.git"
Issues = "https://github.com/company/renchefei-detection/issues"

[project.scripts]
renchefei-detect = "run_inference:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["dev"]

[tool.uv]
dev-dependencies = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "isort>=5.9.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["renchefei"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=dev",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["dev"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/.*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
