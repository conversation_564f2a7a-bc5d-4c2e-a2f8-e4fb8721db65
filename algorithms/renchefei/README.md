# 人车非检测算法 (RenCheFei Detection)

## 🎯 项目概述

这是一个基于 YOLOv5 的人车非检测算法，支持检测图像中的人员、车辆和非机动车。该项目提供完整的本地开发和 Docker 部署解决方案。

## 📁 项目结构

```
renchefei/
├── 📂 dev/                          # 开发环境
│   ├── inference_engine.py          # 推理引擎
│   ├── run_inference.py             # 推理脚本
│   ├── logger_config.py             # 日志配置
│   ├── config.ini                   # 配置文件
│   ├── model_weights.pt             # 模型文件
│   ├── models/                      # YOLOv5 模型模块
│   └── utils/                       # YOLOv5 工具模块
├── 📂 docker/                       # Docker 部署
│   ├── Dockerfile                   # Docker 镜像配置
│   ├── requirements.txt             # Python 依赖
│   └── (同步的代码文件)
├── 📂 scripts/                      # 自动化脚本
│   ├── sync_to_docker.sh            # 同步到 Docker
│   ├── build_docker.sh              # 构建 Docker 镜像
│   ├── deploy.sh                    # 一键部署
│   └── quick_start.sh               # 快速开始
├── 📂 data/                         # 数据目录
│   ├── input/                       # 输入图像
│   └── output/                      # 输出结果
├── 📂 logs/                         # 日志目录
├── pyproject.toml                   # uv 项目配置
└── README.md                        # 项目说明 (本文件)
```

## 🚀 快速开始

### 方式一：交互式菜单 (推荐)
```bash
./scripts/quick_start.sh
```

### 方式二：命令行操作

#### 1. 本地开发
```bash
# 进入开发目录
cd dev

# 配置授权密钥 (编辑 config.ini)
vim config.ini

# 运行推理
uv run python run_inference.py ../data/input/your_image.jpg

# 查看结果
ls ../data/output/
```

#### 2. Docker 部署
```bash
# 同步代码
./scripts/sync_to_docker.sh

# 构建镜像
./scripts/build_docker.sh

# 部署容器
./scripts/deploy.sh

# 运行推理
docker exec renchefei-detector python run_inference.py /app/data/input/your_image.jpg
```

#### 3. 一键操作
```bash
# 一键重新部署
./scripts/deploy.sh --rebuild

# 查看容器状态
./scripts/deploy.sh --status

# 查看日志
./scripts/deploy.sh --logs
```

## ⚙️ 配置说明

### 授权配置 (config.ini)
```ini
[LICENSE]
key = YOUR_UNIQUE_LICENSE_KEY_FOR_THIS_CLIENT

[DETECTION]
confidence_threshold = 0.25
iou_threshold = 0.45
max_detections = 1000
```

### 环境变量
- `IN_DOCKER`: 自动检测是否在 Docker 环境中运行
- 本地环境：自动使用相对路径
- Docker 环境：自动使用容器内路径

## 📊 功能特性

### 检测能力
- ✅ **人员检测** - 识别图像中的人员
- ✅ **车辆检测** - 识别各类机动车辆
- ✅ **非机动车检测** - 识别自行车、电动车等

### 输入支持
- ✅ **单张图像** - JPG, PNG, JPEG 格式
- ✅ **批量图像** - 目录批量处理
- ✅ **视频文件** - MP4, AVI, MOV 等格式

### 输出格式
- ✅ **可视化结果** - 带标注框的图像
- ✅ **结构化数据** - JSON 格式检测结果
- ✅ **统计信息** - 各类别数量统计

### 日志系统
- ✅ **多级日志** - DEBUG, INFO, WARNING, ERROR
- ✅ **性能监控** - 处理时间、检测数量统计
- ✅ **日志轮转** - 自动管理日志文件大小
- ✅ **环境适配** - 本地和 Docker 环境自动切换

## 🛠️ 开发指南

### 本地开发环境
```bash
# 安装 uv (如果未安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 进入开发目录
cd dev

# 安装依赖
uv sync

# 运行测试
uv run python run_inference.py ../data/input/test.jpg
```

### 代码修改流程
1. **本地开发** - 在 `dev/` 目录中修改代码
2. **本地测试** - 验证功能正常
3. **同步代码** - `./scripts/sync_to_docker.sh`
4. **构建镜像** - `./scripts/build_docker.sh`
5. **部署测试** - `./scripts/deploy.sh`

### 添加新功能
1. 修改 `dev/inference_engine.py` - 核心算法逻辑
2. 修改 `dev/run_inference.py` - 接口和流程
3. 更新 `dev/config.ini` - 配置参数
4. 测试并同步到 Docker

## 📋 系统要求

### 本地开发
- **操作系统**: macOS, Linux, Windows
- **Python**: 3.8+
- **包管理器**: uv (推荐) 或 pip
- **GPU**: 可选，支持 CUDA 和 MPS

### Docker 部署
- **Docker**: 20.10+
- **内存**: 4GB+
- **存储**: 10GB+

## 🔧 故障排除

### 常见问题

**1. 授权验证失败**
```bash
# 检查配置文件
cat dev/config.ini

# 检查网络连接
curl -X POST http://127.0.0.1:8000/verify -H "Content-Type: application/json" -d '{"license_key":"your_key"}'
```

**2. 模型加载失败**
```bash
# 检查模型文件
ls -la dev/model_weights.pt

# 检查 PyTorch 版本
python -c "import torch; print(torch.__version__)"
```

**3. Docker 构建失败**
```bash
# 清理 Docker 缓存
docker system prune -f

# 重新构建
./scripts/build_docker.sh --no-cache
```

### 日志查看
```bash
# 本地日志
ls logs/
tail -f logs/renchefei_detailed.log

# Docker 日志
./scripts/deploy.sh --logs
```

## 📈 性能优化

### 本地环境
- **MPS 加速** (Apple Silicon): 自动启用
- **CUDA 加速** (NVIDIA GPU): 自动检测
- **CPU 优化**: 多线程处理

### Docker 环境
- **内存优化**: 合理设置容器内存限制
- **存储优化**: 使用 volume 挂载避免数据丢失
- **网络优化**: 独立网络避免端口冲突

## 📞 技术支持

如有问题，请查看：
1. **日志文件** - `logs/` 目录下的详细日志
2. **配置检查** - 确认 `config.ini` 配置正确
3. **环境验证** - 运行 `./scripts/quick_start.sh` 进行诊断

---

**这是一个完全独立的算法包，可以单独复制给其他人使用！** 🚀
