# 人车非检测算法

基于YOLOv5的高性能人车非检测算法，支持人员、车辆、非机动车等多类目标检测。

## 🚀 快速开始

### 本地运行
```bash
# 安装依赖
uv sync

# 单张图像检测
uv run python src/run_inference.py data/input/test.jpg

# 批量图像检测
uv run python src/run_inference.py data/input/

# 视频检测
uv run python src/run_inference.py data/input/test.mp4 --video
```

### Docker运行
```bash
# 构建镜像
docker build -t renchefei:latest .

# 运行容器
docker run -d --name renchefei \
  -v $(pwd)/data:/app/data \
  renchefei:latest

# 执行检测
docker exec renchefei uv run python src/run_inference.py /app/data/input/test.jpg
```

## 📋 检测类别

支持以下6类目标检测：
- **person** - 人员
- **vehicle** - 车辆
- **bicycle** - 自行车/非机动车
- **plate** - 车牌
- **head** - 人头
- **fall** - 跌倒检测

## ⚙️ 配置说明

配置文件：`config.ini`

```ini
[MODEL]
input_size = 640
num_classes = 6
device = auto

[DETECTION]
confidence_threshold = 0.25
iou_threshold = 0.45
max_detections = 1000
enable_gpu = true

[OUTPUT]
save_detection_images = true
save_detection_videos = true
output_format = json
```

## 📊 功能特性

- **多类检测**: 支持6类目标同时检测
- **高精度**: 基于YOLOv5优化模型
- **实时处理**: 支持视频实时检测
- **批量处理**: 支持目录批量检测
- **GPU加速**: 支持CUDA和MPS加速
- **易部署**: Docker容器化，一键部署

## 🛠️ 开发环境

- Python 3.11+
- PyTorch 1.13+
- OpenCV 4.8+
- CUDA 11.8+ (可选)

## 📝 模型文件

需要将模型文件 `model_weights.pt` 放置在 `models/` 目录。

## 🔧 使用示例

### 命令行参数
```bash
# 基本检测
python src/run_inference.py input.jpg

# 视频检测（每10帧处理一次）
python src/run_inference.py input.mp4 --video --frame-interval 10

# 实时视频检测
python src/run_inference.py input.mp4 --video --real-time

# 输出检测视频
python src/run_inference.py input.mp4 --video --output-video

# 不保存结果
python src/run_inference.py input.jpg --no-save
```

### 交互模式
```bash
# 启动交互模式
python src/run_inference.py

# 然后按提示选择：
# 1) 单张图像检测
# 2) 批量图像检测  
# 3) 视频检测
# 4) 退出
```

## 🔧 故障排除

### 常见问题
1. **模型加载失败**: 检查 `models/model_weights.pt` 是否存在
2. **GPU不可用**: 检查CUDA安装和PyTorch版本
3. **内存不足**: 降低 `input_size` 或 `max_detections`

### 性能优化
- 使用GPU加速：确保CUDA环境正确
- 调整输入尺寸：根据精度需求调整 `input_size`
- 视频处理：增加 `frame_interval` 提高处理速度
