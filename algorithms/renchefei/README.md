# 人车非检测算法

基于YOLOv5的多目标检测API服务，支持人员、车辆、非机动车、车牌、人头、跌倒等6类目标实时检测。

## 🚀 快速部署

### Docker部署（推荐）
```bash
# 构建并运行
docker build -t renchefei .
docker run -d -p 8002:8002 --name renchefei renchefei

# 访问API文档
curl http://localhost:8002/docs
```

### 本地开发
```bash
uv sync && uv run python src/api_server.py
```

## 📋 API接口

| 接口 | 方法 | 功能 | 参数 |
|------|------|------|------|
| `/api/v1/health` | GET | 健康检查 | - |
| `/api/v1/info` | GET | 算法信息 | - |
| `/api/v1/classes` | GET | 获取支持类别 | - |
| `/api/v1/detect` | POST | 目标检测 | `file`, `conf_threshold`, `iou_threshold` |
| `/api/v1/detect_batch` | POST | 批量检测 | `files[]` |
| `/api/v1/detect_by_class` | POST | 按类别检测 | `file`, `target_classes[]`, `conf_threshold` |
| `/api/v1/statistics` | POST | 检测统计分析 | `files[]` |

### API调用示例
```bash
# 单张图像检测
curl -X POST http://localhost:8002/api/v1/detect \
  -F "file=@image.jpg" \
  -F "conf_threshold=0.5"

# 批量检测
curl -X POST http://localhost:8002/api/v1/detect_batch \
  -F "files=@image1.jpg" \
  -F "files=@image2.jpg"
```

### 响应格式
```json
{
  "success": true,
  "message": "检测完成，发现 3 个目标",
  "data": {
    "num_detections": 3,
    "processing_time": 0.15,
    "detections": [
      {
        "class_name": "person",
        "confidence": 0.89,
        "bbox": [100, 50, 200, 300]
      }
    ],
    "class_counts": {
      "person": 2,
      "vehicle": 1
    }
  }
}
```

## 🎯 检测类别

| 类别 | 英文名 | 说明 |
|------|--------|------|
| 人员 | person | 行人检测 |
| 车辆 | vehicle | 机动车辆 |
| 非机动车 | bicycle | 自行车、电动车 |
| 车牌 | plate | 车牌识别 |
| 人头 | head | 人头检测 |
| 跌倒 | fall | 跌倒行为检测 |

## ⚙️ 配置参数

编辑 `config.ini` 调整检测参数：

```ini
[MODEL]
input_size = 640              # 输入图像尺寸
num_classes = 6               # 检测类别数
device = auto                 # 设备选择(auto/cpu/cuda)

[DETECTION]
confidence_threshold = 0.25   # 置信度阈值
iou_threshold = 0.45         # IoU阈值
max_detections = 1000        # 最大检测数量
enable_gpu = true            # 启用GPU加速

[OUTPUT]
save_detection_images = true # 保存检测结果图像
output_format = json         # 输出格式
```

## 📊 技术规格

- **检测模型**: YOLOv5 (640x640)
- **检测类别**: 6类目标
- **推理框架**: PyTorch
- **支持格式**: JPG, PNG, BMP
- **并发处理**: 支持批量API调用
- **GPU加速**: 支持CUDA和MPS

## 🔧 性能调优

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `input_size` | 检测精度vs速度 | 640(精度) / 416(速度) |
| `confidence_threshold` | 检测敏感度 | 0.25-0.5 |
| `iou_threshold` | 重叠抑制 | 0.45-0.6 |
| `max_detections` | 最大检测数 | 100-1000 |

## 📝 依赖要求

- **运行环境**: Python 3.11+, Docker 20.10+
- **核心依赖**: PyTorch 1.13+, OpenCV 4.8+, FastAPI 0.104+
- **模型文件**: `model_weights.pt` 需放置在 `models/` 目录
- **硬件要求**: 4GB+ RAM, GPU可选(推荐)
