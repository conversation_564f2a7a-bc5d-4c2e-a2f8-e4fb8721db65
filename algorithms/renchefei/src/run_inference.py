#!/usr/bin/env python3
"""
人车非检测算法 - 客户调用示例
使用说明：
1. 确保已安装所需依赖：pip install torch torchvision opencv-python pillow requests numpy
2. 在 src/config/dev.ini 中配置您的授权密钥
3. 运行此脚本进行推理测试
"""

import os
import sys
import configparser
import argparse
from pathlib import Path
from PIL import Image
import cv2
import time
import json

# 导入日志系统
try:
    from logger_config import get_logger, get_logger_instance
    logger = get_logger()
    logger_instance = get_logger_instance()
except ImportError as e:
    print(f"错误：无法导入日志模块: {e}")
    sys.exit(1)

# 导入推理引擎
try:
    from inference_engine import RenchefeiDetectionEngine
except ImportError as e:
    logger.error(f"无法导入推理引擎模块: {e}")
    logger.error("请确保 inference_engine.py 文件在同一目录下")
    sys.exit(1)


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()

    # 获取脚本所在目录的父目录，然后找到配置文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)

    # 优先使用项目根目录下的 config.ini
    config_file = os.path.join(project_root, 'config.ini')

    # 如果不存在，尝试 src/config/dev.ini
    if not os.path.exists(config_file):
        alt_config = os.path.join(script_dir, 'config', 'dev.ini')
        if os.path.exists(alt_config):
            config_file = alt_config
        else:
            logger.error(f"配置文件不存在，已尝试以下位置:")
            logger.error(f"  - {config_file}")
            logger.error(f"  - {alt_config}")
            logger.error("请创建配置文件并填入您的授权密钥")
            return None, None, None, None

    if not os.path.exists(config_file):
        logger.error(f"配置文件 {config_file} 不存在")
        logger.error("请创建配置文件并填入您的授权密钥")
        return None, None, None, None

    try:
        config.read(config_file, encoding='utf-8')

        # 读取检测参数
        conf_thres = config.getfloat('DETECTION', 'confidence_threshold', fallback=0.25)
        iou_thres = config.getfloat('DETECTION', 'iou_threshold', fallback=0.45)
        max_det = config.getint('DETECTION', 'max_detections', fallback=1000)

        logger.info(f"配置加载成功: conf_thres={conf_thres}, iou_thres={iou_thres}, max_det={max_det}")
        return conf_thres, iou_thres, max_det
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return None, None, None


def draw_detections(image, detections, save_path=None):
    """在图像上绘制检测结果"""
    if isinstance(image, str):
        img = cv2.imread(image)
    else:
        img = image.copy()
    
    # 定义颜色映射
    colors = {
        'person': (0, 255, 0),      # 绿色
        'vehicle': (255, 0, 0),     # 蓝色
        'bicycle': (0, 255, 255),   # 黄色
        'plate': (255, 0, 255),     # 紫色
        'head': (0, 128, 255),      # 橙色
        'fall': (0, 0, 255)         # 红色
    }
    
    for detection in detections:
        x1, y1, x2, y2 = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # 获取颜色
        color = colors.get(class_name, (128, 128, 128))
        
        # 绘制边界框
        cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
        
        # 绘制标签
        label = f"{class_name}: {confidence:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
        cv2.rectangle(img, (x1, y1 - label_size[1] - 10), (x1 + label_size[0], y1), color, -1)
        cv2.putText(img, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
    
    if save_path:
        cv2.imwrite(save_path, img)
        print(f"检测结果已保存到: {save_path}")
    
    return img


def process_single_image(engine, image_path, save_result=False, output_dir=None):
    """处理单张图像"""
    logger_instance.log_inference_start(image_path)

    if not os.path.exists(image_path):
        logger.error(f"图像文件不存在: {image_path}")
        return
    
    try:
        result = engine.predict(image_path)

        # 记录推理结果
        logger_instance.log_inference_result(
            image_path,
            result['num_detections'],
            result['processing_time']
        )

        logger.info("检测结果:")
        logger.info(f"  检测到 {result['num_detections']} 个目标")
        logger.info(f"  处理时间: {result['processing_time']*1000:.2f}ms")
        
        if result['class_counts']:
            logger.info("  类别统计:")
            for class_name, count in result['class_counts'].items():
                logger.info(f"    {class_name}: {count}")

        logger.info("  详细检测结果:")
        for i, detection in enumerate(result['detections'], 1):
            bbox = detection['bbox']
            logger.info(f"    [{i}] {detection['class_name']}: "
                  f"置信度={detection['confidence']:.3f}, "
                  f"位置=({bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]})")
        
        # 保存可视化结果到 output 目录
        if save_result and result['detections']:
            # 自动根据环境切换路径
            if output_dir is None:
                if os.getenv("IN_DOCKER"):
                    output_dir = "/app/data/output"
                else:
                    # 获取项目根目录（renchefei目录）
                    script_dir = Path(__file__).parent
                    project_root = script_dir.parent
                    output_dir = str(project_root / "data" / "output")

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出文件名
            input_filename = os.path.basename(image_path)
            name, ext = os.path.splitext(input_filename)
            output_filename = f"{name}_detected.jpg"
            output_path = os.path.join(output_dir, output_filename)

            draw_detections(image_path, result['detections'], output_path)
            logger.info(f"检测结果已保存: {output_path}")

    except Exception as e:
        logger_instance.log_error("预测失败", e)


def process_batch_images(engine, image_dir, save_results=False, output_dir=None):
    """处理批量图像"""
    if not os.path.exists(image_dir):
        print(f"错误：图像目录不存在: {image_dir}")
        return
    
    # 支持的图像格式
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
    
    # 获取所有图像文件
    image_files = []
    for file in os.listdir(image_dir):
        if file.lower().endswith(supported_formats):
            image_files.append(os.path.join(image_dir, file))
    
    if not image_files:
        print(f"在目录 {image_dir} 中未找到支持的图像文件")
        return
    
    print(f"\n开始批量处理 {len(image_files)} 张图像...")
    
    total_detections = 0
    total_time = 0
    class_stats = {}
    
    # 创建输出目录
    if save_results:
        # 自动根据环境切换路径
        if output_dir is None:
            if os.getenv("IN_DOCKER"):
                output_dir = "/app/data/output"
            else:
                # 获取项目根目录（renchefei目录）
                script_dir = Path(__file__).parent
                project_root = script_dir.parent
                output_dir = str(project_root / "data" / "output")

        os.makedirs(output_dir, exist_ok=True)
    
    for i, image_path in enumerate(image_files, 1):
        try:
            start_time = time.time()
            result = engine.predict(image_path)
            end_time = time.time()
            
            processing_time = end_time - start_time
            total_time += processing_time
            total_detections += result['num_detections']
            
            # 统计类别
            for class_name, count in result['class_counts'].items():
                class_stats[class_name] = class_stats.get(class_name, 0) + count
            
            print(f"[{i}/{len(image_files)}] {os.path.basename(image_path)}: "
                  f"{result['num_detections']} 个目标 "
                  f"(时间: {processing_time*1000:.1f}ms)")
            
            # 保存检测结果
            if save_results and result['detections']:
                output_path = os.path.join(output_dir, f"detected_{os.path.basename(image_path)}")
                draw_detections(image_path, result['detections'], output_path)
                
        except Exception as e:
            print(f"[{i}/{len(image_files)}] {os.path.basename(image_path)}: 处理失败 - {e}")
    
    print(f"\n批量处理完成:")
    print(f"  总图像数: {len(image_files)}")
    print(f"  总检测数: {total_detections}")
    print(f"  平均处理时间: {(total_time/len(image_files))*1000:.2f}ms")
    
    if class_stats:
        print(f"  类别统计:")
        for class_name, count in sorted(class_stats.items()):
            print(f"    {class_name}: {count}")


def process_video(engine, video_path, save_result=False, output_dir=None, frame_interval=10):
    """处理视频文件

    Args:
        engine: 推理引擎
        video_path: 视频文件路径
        save_result: 是否保存结果视频
        output_dir: 输出目录
        frame_interval: 帧间隔，每N帧处理一次（默认10）
    """
    if not os.path.exists(video_path):
        print(f"错误：视频文件不存在: {video_path}")
        return
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误：无法打开视频文件: {video_path}")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"\n处理视频: {video_path}")
    print(f"分辨率: {width}x{height}, 帧率: {fps}, 总帧数: {total_frames}")
    print(f"帧间隔: 每 {frame_interval} 帧处理一次")
    
    # 设置输出视频
    if save_result:
        # 自动根据环境切换路径
        if output_dir is None:
            if os.getenv("IN_DOCKER"):
                output_dir = "/app/data/output"
            else:
                # 获取项目根目录（renchefei目录）
                script_dir = Path(__file__).parent
                project_root = script_dir.parent
                output_dir = str(project_root / "data" / "output")

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 生成输出文件名
        input_filename = os.path.basename(video_path)
        name, ext = os.path.splitext(input_filename)
        output_filename = f"{name}_detected.mp4"
        output_path = os.path.join(output_dir, output_filename)

        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    total_time = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 根据设置的帧间隔处理
            if frame_count % frame_interval == 0:
                start_time = time.time()
                result = engine.predict(frame)
                end_time = time.time()
                
                processing_time = end_time - start_time
                total_time += processing_time
                
                print(f"帧 {frame_count}/{total_frames}: "
                      f"{result['num_detections']} 个目标 "
                      f"(时间: {processing_time*1000:.1f}ms)")
                
                # 绘制检测结果
                if result['detections']:
                    frame = draw_detections(frame, result['detections'])
            
            # 保存帧
            if save_result:
                out.write(frame)
            
            # 显示进度
            if frame_count % 100 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"处理进度: {progress:.1f}%")
    
    except KeyboardInterrupt:
        print("\n用户中断处理")
    
    finally:
        cap.release()
        if save_result:
            out.release()
            print(f"检测结果视频已保存到: {output_path}")
        
        if frame_count > 0:
            processed_frames = frame_count // frame_interval
            avg_time = (total_time / processed_frames) * 1000 if processed_frames > 0 else 0
            print(f"平均处理时间: {avg_time:.2f}ms/帧 (处理了 {processed_frames} 帧)")


def interactive_mode(engine, output_dir=None):
    """交互模式"""
    print("\n=== 交互模式 ===")
    print("输入图像/视频路径进行检测，输入 'quit' 退出")
    print("命令格式:")
    print("  图像: path/to/image.jpg")
    print("  视频: path/to/video.mp4")
    print("  批量: path/to/directory/")
    
    while True:
        try:
            input_path = input("\n请输入路径: ").strip()
            
            if input_path.lower() in ['quit', 'exit', 'q']:
                print("退出交互模式")
                break
            
            if not input_path:
                continue
            
            if os.path.isfile(input_path):
                # 检查是否为视频文件
                video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv')
                if input_path.lower().endswith(video_extensions):
                    process_video(engine, input_path, save_result=True, output_dir=output_dir)
                else:
                    process_single_image(engine, input_path, save_result=True, output_dir=output_dir)
            elif os.path.isdir(input_path):
                process_batch_images(engine, input_path, save_results=True, output_dir=output_dir)
            else:
                print(f"错误：路径不存在: {input_path}")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出交互模式")
            break
        except Exception as e:
            print(f"发生错误: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("人车非检测算法 - 推理测试程序")
    print("=" * 60)
    
    # 加载配置
    conf_thres, iou_thres, max_det = load_config()
    if conf_thres is None:
        return

    # 初始化推理引擎
    try:
        print("正在初始化推理引擎...")
        engine = RenchefeiDetectionEngine()

        # 设置检测参数
        engine.set_detection_params(conf_thres=conf_thres, iou_thres=iou_thres, max_det=max_det)
        print(f"✓ 推理引擎初始化成功")
        print(f"  检测参数: conf={conf_thres}, iou={iou_thres}, max_det={max_det}")
    except Exception as e:
        print(f"✗ 推理引擎初始化失败: {e}")
        return
    
    # 自动根据环境切换路径
    if os.getenv("IN_DOCKER"):
        output_dir = "/app/data/output"
    else:
        # 获取项目根目录（renchefei目录）
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
        output_dir = str(project_root / "data" / "output")

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='人车非检测算法推理程序')
    parser.add_argument('input', nargs='?', help='输入文件或目录路径')
    parser.add_argument('--video', action='store_true', help='强制作为视频处理')
    parser.add_argument('--frame-interval', type=int, default=10,
                       help='视频处理帧间隔，每N帧处理一次 (默认: 10)')
    parser.add_argument('--real-time', action='store_true',
                       help='实时处理模式 (等同于 --frame-interval 1)')
    parser.add_argument('--output-video', action='store_true',
                       help='输出检测结果视频')
    parser.add_argument('--no-save', action='store_true',
                       help='不保存结果文件')

    args = parser.parse_args()

    # 处理实时模式
    if args.real_time:
        args.frame_interval = 1

    # 检查输入参数
    if args.input:
        input_path = args.input

        if os.path.isfile(input_path):
            # 检查是否为视频文件
            video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv')
            is_video = args.video or input_path.lower().endswith(video_extensions)

            if is_video:
                # 视频默认保存结果，除非用户明确指定不保存
                save_result = not args.no_save
                process_video(engine, input_path, save_result=save_result,
                          output_dir=output_dir, frame_interval=args.frame_interval)
            else:
                save_result = not args.no_save
                process_single_image(engine, input_path, save_result=save_result, output_dir=output_dir)
        elif os.path.isdir(input_path):
            save_results = not args.no_save
            process_batch_images(engine, input_path, save_results=save_results, output_dir=output_dir)
        else:
            print(f"错误：路径不存在: {input_path}")
    else:
        # 交互模式
        interactive_mode(engine, output_dir=output_dir)


if __name__ == "__main__":
    main()
