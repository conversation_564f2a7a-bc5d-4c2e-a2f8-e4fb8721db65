#!/usr/bin/env python3
"""
人车非检测算法 - API服务器
提供RESTful API接口用于人车非目标检测
"""

import os
import io
import time
from typing import List, Optional
import configparser

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image
import cv2

# 导入推理引擎
from inference_engine import RenchefeiDetectionEngine
from logger_config import get_logger

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="人车非检测算法API",
    description="基于YOLOv5的人车非目标检测API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[RenchefeiDetectionEngine] = None


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[dict] = None
    error: Optional[str] = None
    timestamp: str
    processing_time: Optional[float] = None


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_file = "config.ini"
    
    try:
        config.read(config_file, encoding='utf-8')
        conf_thres = config.getfloat('DETECTION', 'confidence_threshold', fallback=0.25)
        iou_thres = config.getfloat('DETECTION', 'iou_threshold', fallback=0.45)
        max_det = config.getint('DETECTION', 'max_detections', fallback=1000)
        return conf_thres, iou_thres, max_det
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return 0.25, 0.45, 1000


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化人车非检测引擎...")
        engine = RenchefeiDetectionEngine()
        
        # 加载配置参数
        conf_thres, iou_thres, max_det = load_config()
        engine.set_detection_params(conf_thres=conf_thres, iou_thres=iou_thres, max_det=max_det)
        
        logger.info("人车非检测引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health")
async def health_check():
    """健康检查接口"""
    return APIResponse(
        success=True,
        message="服务运行正常",
        data={
            "service": "人车非检测算法",
            "status": "healthy",
            "engine_loaded": engine is not None
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.get("/api/v1/info")
async def get_algorithm_info():
    """获取算法信息"""
    return APIResponse(
        success=True,
        message="算法信息获取成功",
        data={
            "name": "人车非检测算法",
            "version": "1.0.0",
            "description": "基于YOLOv5的人车非目标检测算法",
            "capabilities": [
                "人员检测",
                "车辆检测", 
                "非机动车检测",
                "车牌检测",
                "人头检测",
                "跌倒检测"
            ],
            "classes": ["person", "vehicle", "bicycle", "plate", "head", "fall"],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"]
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.post("/api/v1/detect")
async def detect_objects(
    file: UploadFile = File(...),
    conf_threshold: float = Form(default=None),
    iou_threshold: float = Form(default=None)
):
    """目标检测接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    
    try:
        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为OpenCV格式
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # 执行推理
        results = engine.predict(
            image_cv,
            conf_thres=conf_threshold,
            iou_thres=iou_threshold
        )
        
        processing_time = time.time() - start_time
        
        return APIResponse(
            success=True,
            message=f"检测完成，发现 {results['num_detections']} 个目标",
            data=results,
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"目标检测失败: {e}")
        return APIResponse(
            success=False,
            message="目标检测失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/detect_batch")
async def detect_batch(files: List[UploadFile] = File(...)):
    """批量检测接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    results = []
    
    try:
        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
                
                # 执行推理
                result = engine.predict(image_cv)
                result['filename'] = file.filename
                result['index'] = i
                results.append(result)
                
            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'num_detections': 0,
                    'detections': []
                })
        
        processing_time = time.time() - start_time
        total_detections = sum(r.get('num_detections', 0) for r in results)
        
        return APIResponse(
            success=True,
            message=f"批量检测完成，处理 {len(files)} 张图像，共发现 {total_detections} 个目标",
            data={
                "total_images": len(files),
                "total_detections": total_detections,
                "results": results
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"批量检测失败: {e}")
        return APIResponse(
            success=False,
            message="批量检测失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


if __name__ == "__main__":
    # 启动API服务器
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
        log_level="info"
    )
