2025-07-24 13:47:03 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 13:47:03 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-24 13:47:29 | renchefei | INFO | logger_config.py:113 | 开始推理: ../data/input/image3.png
2025-07-24 13:47:29 | renchefei | INFO | logger_config.py:117 | 推理完成: ../data/input/image3.png | 检测数量: 1 | 耗时: 0.224s
2025-07-24 13:47:29 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.224
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:127 |   处理时间: 223.61ms
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-24 13:47:29 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: ../data/output/image3_detected.jpg
2025-07-24 13:51:10 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 13:51:10 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-24 13:51:47 | renchefei | INFO | logger_config.py:113 | 开始推理: ../data/input/image3.png
2025-07-24 13:51:47 | renchefei | INFO | logger_config.py:117 | 推理完成: ../data/input/image3.png | 检测数量: 1 | 耗时: 0.115s
2025-07-24 13:51:47 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.115
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:127 |   处理时间: 114.92ms
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-24 13:51:47 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: ../data/output/image3_detected.jpg
2025-07-24 14:09:54 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 14:09:54 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-24 14:09:55 | renchefei | INFO | logger_config.py:113 | 开始推理: ../data/input/image3.png
2025-07-24 14:09:55 | renchefei | INFO | logger_config.py:117 | 推理完成: ../data/input/image3.png | 检测数量: 1 | 耗时: 0.089s
2025-07-24 14:09:55 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.089
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:127 |   处理时间: 89.19ms
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-24 14:09:55 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: ../data/output/image3_detected.jpg
2025-07-24 14:12:07 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 14:12:07 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-24 14:12:08 | renchefei | INFO | logger_config.py:113 | 开始推理: ../data/input/image3.png
2025-07-24 14:12:08 | renchefei | INFO | logger_config.py:117 | 推理完成: ../data/input/image3.png | 检测数量: 1 | 耗时: 0.079s
2025-07-24 14:12:08 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.079
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:127 |   处理时间: 78.93ms
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-24 14:12:08 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: ../data/output/image3_detected.jpg
2025-07-24 14:29:13 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 14:29:13 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-24 14:29:15 | renchefei | INFO | logger_config.py:113 | 开始推理: ../data/input/image3.png
2025-07-24 14:29:15 | renchefei | INFO | logger_config.py:117 | 推理完成: ../data/input/image3.png | 检测数量: 1 | 耗时: 0.086s
2025-07-24 14:29:15 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.086
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:127 |   处理时间: 85.84ms
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-24 14:29:15 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: ../data/output/image3_detected.jpg
2025-07-24 06:32:09 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 06:32:09 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-24 06:35:52 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 06:35:52 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-24 06:36:15 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 06:36:15 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-24 14:53:55 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 14:53:55 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-24 14:53:57 | renchefei | INFO | logger_config.py:113 | 开始推理: ../data/input/image3.png
2025-07-24 14:53:57 | renchefei | INFO | logger_config.py:117 | 推理完成: ../data/input/image3.png | 检测数量: 1 | 耗时: 0.101s
2025-07-24 14:53:57 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.101
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:127 |   处理时间: 100.74ms
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-24 14:53:57 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: ../data/output/image3_detected.jpg
2025-07-24 07:01:24 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 07:01:24 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-24 23:29:06 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-24 23:29:06 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-24 23:29:08 | renchefei | INFO | logger_config.py:113 | 开始推理: ../data/input/image3.png
2025-07-24 23:29:08 | renchefei | INFO | logger_config.py:117 | 推理完成: ../data/input/image3.png | 检测数量: 1 | 耗时: 0.106s
2025-07-24 23:29:08 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.106
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:127 |   处理时间: 106.09ms
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-24 23:29:08 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: ../data/output/image3_detected.jpg
2025-07-25 00:40:56 | renchefei | INFO | run_inference.py:60 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 00:40:56 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:10:56 | renchefei | INFO | run_inference.py:60 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:10:56 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:10:58 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 01:10:58 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.081s
2025-07-25 01:10:58 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.081
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:126 | 检测结果:
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:127 |   检测到 1 个目标
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:128 |   处理时间: 81.26ms
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:131 |   类别统计:
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:133 |     vehicle: 1
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:135 |   详细检测结果:
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:138 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 01:10:58 | renchefei | INFO | run_inference.py:164 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 01:16:23 | renchefei | INFO | run_inference.py:60 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:16:23 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:16:24 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 01:16:24 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.087s
2025-07-25 01:16:24 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.087
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:126 | 检测结果:
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:127 |   检测到 1 个目标
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:128 |   处理时间: 86.74ms
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:131 |   类别统计:
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:133 |     vehicle: 1
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:135 |   详细检测结果:
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:138 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 01:16:24 | renchefei | INFO | run_inference.py:164 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 01:20:50 | renchefei | ERROR | run_inference.py:43 | 配置文件 config.ini 不存在
2025-07-25 01:20:50 | renchefei | ERROR | run_inference.py:44 | 请创建配置文件并填入您的授权密钥
2025-07-25 01:21:19 | renchefei | INFO | run_inference.py:60 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:21:19 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:21:20 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 01:21:20 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.075s
2025-07-25 01:21:20 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.075
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:126 | 检测结果:
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:127 |   检测到 1 个目标
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:128 |   处理时间: 75.27ms
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:131 |   类别统计:
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:133 |     vehicle: 1
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:135 |   详细检测结果:
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:138 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 01:21:20 | renchefei | INFO | run_inference.py:164 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 01:22:43 | renchefei | INFO | run_inference.py:60 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:22:43 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:22:45 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 01:22:45 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.086s
2025-07-25 01:22:45 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.086
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:126 | 检测结果:
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:127 |   检测到 1 个目标
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:128 |   处理时间: 85.69ms
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:131 |   类别统计:
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:133 |     vehicle: 1
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:135 |   详细检测结果:
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:138 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 01:22:45 | renchefei | INFO | run_inference.py:164 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 01:29:28 | renchefei | ERROR | run_inference.py:43 | 配置文件 config.ini 不存在
2025-07-25 01:29:28 | renchefei | ERROR | run_inference.py:44 | 请创建配置文件并填入您的授权密钥
2025-07-25 01:31:59 | renchefei | INFO | run_inference.py:77 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:31:59 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:35:05 | renchefei | INFO | run_inference.py:78 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:35:05 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:35:07 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 01:35:07 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.085s
2025-07-25 01:35:07 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.085
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:144 | 检测结果:
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:145 |   检测到 1 个目标
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:146 |   处理时间: 84.70ms
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:149 |   类别统计:
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:151 |     vehicle: 1
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:153 |   详细检测结果:
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:156 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 01:35:07 | renchefei | INFO | run_inference.py:182 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 01:35:16 | renchefei | INFO | run_inference.py:78 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:35:16 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:35:40 | renchefei | INFO | run_inference.py:78 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:35:40 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:35:47 | renchefei | INFO | run_inference.py:78 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:35:47 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:35:59 | renchefei | INFO | run_inference.py:78 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:35:59 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:40:02 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:40:02 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:41:51 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:41:51 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:42:08 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:42:08 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:42:23 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:42:23 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:46:11 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:46:11 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:47:52 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:47:52 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:47:53 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:47:53 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 01:48:34 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 01:48:34 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 09:38:57 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 09:38:57 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 09:38:59 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 09:38:59 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.084s
2025-07-25 09:38:59 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.084
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:147 |   处理时间: 84.25ms
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 09:38:59 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 09:39:31 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 09:39:31 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 09:41:17 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 09:41:17 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 09:41:18 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 09:41:18 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.083s
2025-07-25 09:41:18 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.083
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:147 |   处理时间: 83.11ms
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 09:41:18 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 10:38:35 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 10:38:35 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 10:39:08 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 10:39:08 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 10:39:09 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 10:39:09 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.087s
2025-07-25 10:39:09 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.087
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:147 |   处理时间: 87.04ms
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 10:39:09 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 02:41:50 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 02:41:50 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-25 02:41:52 | renchefei | INFO | logger_config.py:113 | 开始推理: /app/data/input/image3.png
2025-07-25 02:41:53 | renchefei | INFO | logger_config.py:117 | 推理完成: /app/data/input/image3.png | 检测数量: 1 | 耗时: 0.346s
2025-07-25 02:41:53 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.346
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:127 |   处理时间: 346.18ms
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 02:41:53 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: /app/data/output/image3_detected.jpg
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 02:42:17 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-25 02:42:17 | renchefei | INFO | logger_config.py:113 | 开始推理: /app/data/input/image3.png
2025-07-25 02:42:17 | renchefei | INFO | logger_config.py:117 | 推理完成: /app/data/input/image3.png | 检测数量: 1 | 耗时: 0.328s
2025-07-25 02:42:17 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.328
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:127 |   处理时间: 328.24ms
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 02:42:17 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: /app/data/output/image3_detected.jpg
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 02:45:05 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-25 02:45:05 | renchefei | INFO | logger_config.py:113 | 开始推理: /app/data/input/image3.png
2025-07-25 02:45:05 | renchefei | INFO | logger_config.py:117 | 推理完成: /app/data/input/image3.png | 检测数量: 1 | 耗时: 0.218s
2025-07-25 02:45:05 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.218
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:127 |   处理时间: 217.92ms
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 02:45:05 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: /app/data/output/image3_detected.jpg
2025-07-25 03:35:45 | renchefei | INFO | run_inference.py:59 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 03:35:45 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://host.docker.internal:8000/verify
2025-07-25 03:35:45 | renchefei | INFO | logger_config.py:113 | 开始推理: /app/data/input/image3.png
2025-07-25 03:35:46 | renchefei | INFO | logger_config.py:117 | 推理完成: /app/data/input/image3.png | 检测数量: 1 | 耗时: 0.284s
2025-07-25 03:35:46 | renchefei.performance | INFO | logger_config.py:121 | image=image3.png | detections=1 | time=0.284
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:125 | 检测结果:
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:126 |   检测到 1 个目标
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:127 |   处理时间: 284.21ms
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:130 |   类别统计:
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:132 |     vehicle: 1
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:134 |   详细检测结果:
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:137 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 03:35:46 | renchefei | INFO | run_inference.py:160 | 检测结果已保存: /app/data/output/image3_detected.jpg
2025-07-25 11:43:47 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 11:43:47 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 11:43:49 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 11:43:49 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.091s
2025-07-25 11:43:49 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.091
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:147 |   处理时间: 90.51ms
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 11:43:49 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 11:47:50 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 11:47:50 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 11:47:53 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 11:47:53 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.115s
2025-07-25 11:47:53 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.115
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:147 |   处理时间: 114.76ms
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 11:47:53 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 11:52:18 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 11:52:18 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 11:52:19 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 11:52:19 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.113s
2025-07-25 11:52:19 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.113
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:147 |   处理时间: 113.01ms
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 11:52:19 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 11:54:08 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 11:54:08 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 11:54:09 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 11:54:09 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.087s
2025-07-25 11:54:09 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.087
2025-07-25 11:54:09 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 11:54:09 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 11:54:09 | renchefei | INFO | run_inference.py:147 |   处理时间: 86.98ms
2025-07-25 11:54:09 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 11:54:09 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 11:54:09 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 11:54:09 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 11:54:10 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
2025-07-25 12:24:47 | renchefei | INFO | run_inference.py:79 | 配置加载成功: conf_thres=0.25, iou_thres=0.45, max_det=1000
2025-07-25 12:24:47 | renchefei | DEBUG | inference_engine.py:55 | 正在验证授权: http://127.0.0.1:8000/verify
2025-07-25 12:24:49 | renchefei | INFO | logger_config.py:116 | 开始推理: data/input/image3.png
2025-07-25 12:24:49 | renchefei | INFO | logger_config.py:120 | 推理完成: data/input/image3.png | 检测数量: 1 | 耗时: 0.084s
2025-07-25 12:24:49 | renchefei.performance | INFO | logger_config.py:124 | image=image3.png | detections=1 | time=0.084
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:145 | 检测结果:
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:146 |   检测到 1 个目标
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:147 |   处理时间: 84.40ms
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:150 |   类别统计:
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:152 |     vehicle: 1
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:154 |   详细检测结果:
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:157 |     [1] vehicle: 置信度=0.939, 位置=(92,174,210,278)
2025-07-25 12:24:49 | renchefei | INFO | run_inference.py:183 | 检测结果已保存: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/renchefei/data/output/image3_detected.jpg
