# 温州人脸识别算法

高性能人脸识别API服务，基于ONNX Runtime实现人脸检测、特征提取、比对和质量评估。

## 🚀 快速部署

### Docker部署（推荐）
```bash
# 构建并运行
docker build -t wenzhou-face .
docker run -d -p 8001:8001 --name wenzhou-face wenzhou-face

# 访问API文档
curl http://localhost:8001/docs
```

### 本地开发
```bash
uv sync && uv run python src/api_server.py
```

## 📋 API接口

| 接口 | 方法 | 功能 | 参数 |
|------|------|------|------|
| `/api/v1/health` | GET | 健康检查 | - |
| `/api/v1/info` | GET | 算法信息 | - |
| `/api/v1/detect` | POST | 人脸检测 | `file`, `extract_features`, `assess_quality` |
| `/api/v1/compare` | POST | 人脸比对 | `file1`, `file2` |
| `/api/v1/quality` | POST | 质量评估 | `file` |

### API调用示例
```bash
# 人脸检测
curl -X POST http://localhost:8001/api/v1/detect \
  -F "file=@image.jpg" \
  -F "extract_features=true"

# 人脸比对
curl -X POST http://localhost:8001/api/v1/compare \
  -F "file1=@face1.jpg" \
  -F "file2=@face2.jpg"

# 质量评估
curl -X POST http://localhost:8001/api/v1/quality \
  -F "file=@face.jpg"
```

## ⚙️ 配置参数

编辑 `config.ini` 调整算法参数：

```ini
[FACE_DETECTION]
confidence_threshold = 0.7    # 检测置信度阈值
nms_threshold = 0.4          # NMS阈值
input_size = 640             # 输入图像尺寸

[FACE_RECOGNITION]
similarity_threshold = 0.6    # 人脸相似度阈值
feature_dim = 512            # 特征向量维度

[FACE_QUALITY]
enable_quality_check = true  # 启用质量评估
min_quality_score = 0.5      # 最低质量分数

[PROCESSING]
enable_gpu = true            # 启用GPU加速
batch_size = 8               # 批处理大小
```

## 📊 技术规格

- **检测模型**: SCRFD (640x640)
- **识别模型**: ResNet50 (112x112)
- **质量模型**: PFLD-GhostNet (112x112)
- **特征维度**: 512维向量
- **支持格式**: JPG, PNG, BMP
- **并发处理**: 支持批量API调用

## 🔧 性能调优

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `input_size` | 检测精度vs速度 | 640(精度) / 320(速度) |
| `confidence_threshold` | 检测敏感度 | 0.5-0.8 |
| `batch_size` | 批处理大小 | 4-16 |
| `enable_gpu` | GPU加速 | true(有GPU时) |

## 📝 依赖要求

- **运行环境**: Python 3.11+, Docker 20.10+
- **核心依赖**: ONNX Runtime 1.16+, OpenCV 4.8+, FastAPI 0.104+
- **模型文件**: 需放置在 `models/` 目录下
- **硬件要求**: 2GB+ RAM, GPU可选
