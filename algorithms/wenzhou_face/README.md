# 温州人脸识别算法

基于ONNX Runtime的高性能人脸识别算法，支持人脸检测、识别、比对和质量评估。

## 🚀 快速开始

### 本地运行
```bash
# 安装依赖
uv sync

# 启动API服务
uv run python src/api_server.py

# 访问API文档
open http://localhost:8001/docs
```

### Docker运行
```bash
# 构建镜像
docker build -t wenzhou-face:latest .

# 运行容器
docker run -d --name wenzhou-face \
  -p 8001:8001 \
  -v $(pwd)/data:/app/data \
  wenzhou-face:latest
```

## 📋 API接口

### 核心接口
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 算法信息
- `POST /api/v1/detect` - 人脸检测
- `POST /api/v1/compare` - 人脸比对
- `POST /api/v1/quality` - 质量评估

### 使用示例
```bash
# 人脸检测
curl -X POST "http://localhost:8001/api/v1/detect" \
  -F "file=@test.jpg"

# 人脸比对
curl -X POST "http://localhost:8001/api/v1/compare" \
  -F "file1=@face1.jpg" \
  -F "file2=@face2.jpg"
```

## ⚙️ 配置说明

配置文件：`config.ini`

```ini
[FACE_DETECTION]
confidence_threshold = 0.7
nms_threshold = 0.4
input_size = 640

[FACE_RECOGNITION]
similarity_threshold = 0.6
feature_dim = 512

[FACE_QUALITY]
enable_quality_check = true
min_quality_score = 0.5
```

## 📊 功能特性

- **人脸检测**: 基于SCRFD模型，支持多人脸检测
- **人脸识别**: 基于ResNet50特征提取
- **质量评估**: 支持模糊度、光照、姿态评估
- **高性能**: ONNX Runtime优化，支持CPU/GPU
- **易部署**: Docker容器化，一键部署

## 🛠️ 开发环境

- Python 3.11+
- ONNX Runtime 1.16+
- OpenCV 4.8+
- FastAPI 0.104+

## 📝 模型文件

需要将以下模型文件放置在 `models/` 目录：
- `scrfd_10g_tykjanimal_240322.onnx` - 人脸检测模型
- `face_feature_Resnet50_zsq_20240201.onnx` - 人脸识别模型
- `PFLD_GhostNet_Slim_112_1_opt_20240117.onnx` - 质量评估模型

## 🔧 故障排除

### 常见问题
1. **模型加载失败**: 检查模型文件是否存在
2. **API响应慢**: 考虑启用GPU加速
3. **内存不足**: 调整batch_size参数

### 性能优化
- 使用GPU加速：设置 `enable_gpu = true`
- 调整输入尺寸：根据需求调整 `input_size`
- 批量处理：增加 `batch_size` 值
