#!/usr/bin/env python3
"""
温州人脸识别算法 - API服务器
提供RESTful API接口用于人脸检测、识别、比对和质量评估
"""

import os
import io
import json
import time
from typing import List, Optional
from pathlib import Path

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image

# 导入推理引擎
from inference_engine import WenzhouFaceEngine
from logger_config import get_logger

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="温州人脸识别算法API",
    description="基于深度学习的人脸检测、识别、比对和质量评估API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[WenzhouFaceEngine] = None


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[dict] = None
    error: Optional[str] = None
    timestamp: str
    processing_time: Optional[float] = None


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化温州人脸识别引擎...")
        config_path = "config.ini"
        engine = WenzhouFaceEngine(config_path)
        logger.info("温州人脸识别引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health")
async def health_check():
    """健康检查接口"""
    return APIResponse(
        success=True,
        message="服务运行正常",
        data={
            "service": "温州人脸识别算法",
            "status": "healthy",
            "engine_loaded": engine is not None
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.get("/api/v1/info")
async def get_algorithm_info():
    """获取算法信息"""
    return APIResponse(
        success=True,
        message="算法信息获取成功",
        data={
            "name": "温州人脸识别算法",
            "version": "1.0.0",
            "description": "基于深度学习的人脸检测、识别、比对和质量评估",
            "capabilities": [
                "人脸检测",
                "人脸特征提取", 
                "人脸质量评估",
                "人脸比对"
            ],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"]
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.post("/api/v1/detect")
async def detect_faces(
    file: UploadFile = File(...),
    extract_features: bool = Form(default=True),
    assess_quality: bool = Form(default=False)
):
    """人脸检测接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    
    try:
        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为numpy数组
        image_array = np.array(image)
        
        # 执行推理
        results = engine.process_image_array(
            image_array,
            extract_features=extract_features,
            assess_quality=assess_quality
        )
        
        processing_time = time.time() - start_time
        
        return APIResponse(
            success=True,
            message=f"检测完成，发现 {results['num_faces']} 张人脸",
            data=results,
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"人脸检测失败: {e}")
        return APIResponse(
            success=False,
            message="人脸检测失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/compare")
async def compare_faces(
    file1: UploadFile = File(...),
    file2: UploadFile = File(...)
):
    """人脸比对接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    
    try:
        # 读取两张图像
        image1_data = await file1.read()
        image2_data = await file2.read()
        
        image1 = np.array(Image.open(io.BytesIO(image1_data)))
        image2 = np.array(Image.open(io.BytesIO(image2_data)))
        
        # 提取特征
        results1 = engine.process_image_array(image1, extract_features=True)
        results2 = engine.process_image_array(image2, extract_features=True)
        
        if results1['num_faces'] == 0:
            raise ValueError("图像1中未检测到人脸")
        if results2['num_faces'] == 0:
            raise ValueError("图像2中未检测到人脸")
        
        # 获取特征向量
        feature1 = np.array(results1['faces'][0]['feature'])
        feature2 = np.array(results2['faces'][0]['feature'])
        
        # 计算相似度
        similarity = engine.compare_faces(feature1, feature2)
        threshold = engine.config.getfloat('FACE_RECOGNITION', 'similarity_threshold', fallback=0.6)
        is_same_person = similarity >= threshold
        
        processing_time = time.time() - start_time
        
        return APIResponse(
            success=True,
            message="人脸比对完成",
            data={
                "similarity": float(similarity),
                "threshold": threshold,
                "is_same_person": is_same_person,
                "faces_in_image1": results1['num_faces'],
                "faces_in_image2": results2['num_faces']
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"人脸比对失败: {e}")
        return APIResponse(
            success=False,
            message="人脸比对失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/quality")
async def assess_quality(file: UploadFile = File(...)):
    """人脸质量评估接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()

    try:
        # 读取图像
        image_data = await file.read()
        image = np.array(Image.open(io.BytesIO(image_data)))

        # 执行质量评估
        results = engine.process_image_array(image, assess_quality=True)

        if results['num_faces'] == 0:
            raise ValueError("图像中未检测到人脸")

        processing_time = time.time() - start_time

        return APIResponse(
            success=True,
            message="质量评估完成",
            data={
                "num_faces": results['num_faces'],
                "faces": results['faces']
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"质量评估失败: {e}")
        return APIResponse(
            success=False,
            message="质量评估失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/extract_features")
async def extract_features(file: UploadFile = File(...)):
    """人脸特征提取接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()

    try:
        # 读取图像
        image_data = await file.read()
        image = np.array(Image.open(io.BytesIO(image_data)))

        # 执行特征提取
        results = engine.process_image_array(image, extract_features=True)

        if results['num_faces'] == 0:
            raise ValueError("图像中未检测到人脸")

        processing_time = time.time() - start_time

        return APIResponse(
            success=True,
            message=f"特征提取完成，提取了 {results['num_faces']} 张人脸的特征",
            data={
                "num_faces": results['num_faces'],
                "faces": results['faces']
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"特征提取失败: {e}")
        return APIResponse(
            success=False,
            message="特征提取失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/batch_detect")
async def batch_detect(
    files: List[UploadFile] = File(...),
    extract_features: bool = Form(default=True),
    assess_quality: bool = Form(default=False)
):
    """批量人脸检测接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()
    results = []

    try:
        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = np.array(Image.open(io.BytesIO(image_data)))

                # 执行检测
                result = engine.process_image_array(
                    image,
                    extract_features=extract_features,
                    assess_quality=assess_quality
                )
                result['filename'] = file.filename
                result['index'] = i
                results.append(result)

            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'num_faces': 0,
                    'faces': []
                })

        processing_time = time.time() - start_time
        total_faces = sum(r.get('num_faces', 0) for r in results)

        return APIResponse(
            success=True,
            message=f"批量检测完成，处理 {len(files)} 张图像，共发现 {total_faces} 张人脸",
            data={
                "total_images": len(files),
                "total_faces": total_faces,
                "results": results
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"批量检测失败: {e}")
        return APIResponse(
            success=False,
            message="批量检测失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/compare_multiple")
async def compare_multiple(files: List[UploadFile] = File(...)):
    """多人脸比对接口 - 比对多张图像中的所有人脸"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    if len(files) < 2:
        raise HTTPException(status_code=400, detail="至少需要2张图像进行比对")

    start_time = time.time()

    try:
        # 提取所有图像的人脸特征
        all_faces = []
        for i, file in enumerate(files):
            image_data = await file.read()
            image = np.array(Image.open(io.BytesIO(image_data)))

            results = engine.process_image_array(image, extract_features=True)

            for j, face in enumerate(results['faces']):
                all_faces.append({
                    'image_index': i,
                    'image_name': file.filename,
                    'face_index': j,
                    'feature': face['feature'],
                    'bbox': face['bbox'],
                    'confidence': face['confidence']
                })

        # 计算所有人脸之间的相似度
        comparisons = []
        threshold = engine.config.getfloat('FACE_RECOGNITION', 'similarity_threshold', fallback=0.6)

        for i in range(len(all_faces)):
            for j in range(i + 1, len(all_faces)):
                face1 = all_faces[i]
                face2 = all_faces[j]

                feature1 = np.array(face1['feature'])
                feature2 = np.array(face2['feature'])

                similarity = engine.compare_faces(feature1, feature2)
                is_same_person = similarity >= threshold

                comparisons.append({
                    'face1': {
                        'image_name': face1['image_name'],
                        'image_index': face1['image_index'],
                        'face_index': face1['face_index']
                    },
                    'face2': {
                        'image_name': face2['image_name'],
                        'image_index': face2['image_index'],
                        'face_index': face2['face_index']
                    },
                    'similarity': float(similarity),
                    'is_same_person': is_same_person
                })

        processing_time = time.time() - start_time

        return APIResponse(
            success=True,
            message=f"多人脸比对完成，共比对 {len(all_faces)} 张人脸",
            data={
                "total_faces": len(all_faces),
                "total_comparisons": len(comparisons),
                "threshold": threshold,
                "faces": all_faces,
                "comparisons": comparisons
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"多人脸比对失败: {e}")
        return APIResponse(
            success=False,
            message="多人脸比对失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


if __name__ == "__main__":
    # 启动API服务器
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
