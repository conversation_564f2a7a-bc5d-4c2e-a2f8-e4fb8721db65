"""
温州人脸识别推理引擎
包含授权验证逻辑的核心推理模块
注意：此文件在交付前需要使用 pyarmor 进行代码混淆处理
"""

import os
import time
import json
import configparser
import numpy as np
import cv2
import onnxruntime as ort
import requests
import hashlib
import threading
import skimage.transform
from datetime import datetime, timedelta
from PIL import Image
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union

# 导入日志系统
try:
    from logger_config import get_logger, get_logger_instance
    logger = get_logger()
    logger_instance = get_logger_instance()
except ImportError:
    # 如果日志模块不可用，使用标准 logging
    import logging
    logger = logging.getLogger(__name__)
    logger_instance = None


class PoseEstimator:
    """简化的头部姿态估计器"""

    def __init__(self):
        # 3D模型点 (简化的人脸模型)
        self.model_points = np.array([
            (0.0, 0.0, 0.0),             # 鼻尖
            (0.0, -330.0, -65.0),        # 下巴
            (-225.0, 170.0, -135.0),     # 左眼左角
            (225.0, 170.0, -135.0),      # 右眼右角
            (-150.0, -150.0, -125.0),    # 左嘴角
            (150.0, -150.0, -125.0)      # 右嘴角
        ])

    def solve_pose_by_68_points(self, landmarks):
        """通过68个关键点求解头部姿态"""
        # 选择关键的6个点
        image_points = np.array([
            landmarks[30],     # 鼻尖
            landmarks[8],      # 下巴
            landmarks[36],     # 左眼左角
            landmarks[45],     # 右眼右角
            landmarks[48],     # 左嘴角
            landmarks[54]      # 右嘴角
        ], dtype="double")

        # 相机内参矩阵 (假设)
        focal_length = 500
        center = (320, 240)
        camera_matrix = np.array([
            [focal_length, 0, center[0]],
            [0, focal_length, center[1]],
            [0, 0, 1]
        ], dtype="double")

        # 畸变系数
        dist_coeffs = np.zeros((4, 1))

        # 求解PnP
        success, rotation_vector, translation_vector = cv2.solvePnP(
            self.model_points, image_points, camera_matrix, dist_coeffs
        )

        return rotation_vector, translation_vector

    def get_euler_angle(self, rotation_vector):
        """从旋转向量获取欧拉角"""
        # 将旋转向量转换为旋转矩阵
        rotation_matrix, _ = cv2.Rodrigues(rotation_vector)

        # 计算欧拉角
        sy = np.sqrt(rotation_matrix[0, 0] * rotation_matrix[0, 0] + rotation_matrix[1, 0] * rotation_matrix[1, 0])

        singular = sy < 1e-6

        if not singular:
            x = np.arctan2(rotation_matrix[2, 1], rotation_matrix[2, 2])
            y = np.arctan2(-rotation_matrix[2, 0], sy)
            z = np.arctan2(rotation_matrix[1, 0], rotation_matrix[0, 0])
        else:
            x = np.arctan2(-rotation_matrix[1, 2], rotation_matrix[1, 1])
            y = np.arctan2(-rotation_matrix[2, 0], sy)
            z = 0

        # 转换为度数
        return np.degrees(x), np.degrees(y), np.degrees(z)


class LicenseManager:
    """授权管理器"""

    def __init__(self, license_key, license_server_url=None):
        self.license_key = license_key

        # 自动根据环境切换授权服务器地址
        if license_server_url is None:
            if os.getenv("IN_DOCKER"):
                self.license_server_url = "http://host.docker.internal:8000/verify"
            else:
                self.license_server_url = "http://127.0.0.1:8000/verify"
        else:
            self.license_server_url = license_server_url
        self.last_check_time = None
        self.check_interval = 3600  # 1小时检查一次

    def verify_license(self) -> bool:
        """验证授权"""
        try:
            # 检查是否需要重新验证
            current_time = datetime.now()
            if (self.last_check_time and 
                current_time - self.last_check_time < timedelta(seconds=self.check_interval)):
                return True

            # 发送验证请求
            payload = {"license_key": self.license_key}
            response = requests.post(
                self.license_server_url,
                json=payload,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "valid":
                    self.last_check_time = current_time
                    if logger_instance:
                        logger_instance.log_license_check(True, result.get("message", ""))
                    return True
                else:
                    if logger_instance:
                        logger_instance.log_license_check(False, result.get("message", ""))
                    return False
            else:
                if logger_instance:
                    logger_instance.log_license_check(False, f"HTTP {response.status_code}")
                return False

        except Exception as e:
            if logger_instance:
                logger_instance.log_license_check(False, f"网络错误: {str(e)}")
            return False


class FaceDetectionModel:
    """基于SCRFD的人脸检测模型"""

    def __init__(self, model_path: str, confidence_threshold: float = 0.7,
                 nms_threshold: float = 0.4, input_size: int = 640):
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.input_size = (input_size, input_size)
        self.session = None
        self.center_cache = {}
        self.batched = False
        self.use_kps = False
        self.fmc = 3
        self._feat_stride_fpn = [8, 16, 32]
        self._num_anchors = 2
        self._load_model()

    def _get_optimal_providers(self):
        """智能选择最优的 ONNX Runtime providers"""
        import platform

        available_providers = ort.get_available_providers()
        optimal_providers = []

        # 根据平台选择最优 providers
        system = platform.system().lower()
        machine = platform.machine().lower()

        if system == 'darwin':  # macOS
            if 'arm64' in machine or 'aarch64' in machine:  # Apple Silicon (M1/M2/M3)
                # 优先使用 CoreML，然后是 CPU
                if 'CoreMLExecutionProvider' in available_providers:
                    optimal_providers.append('CoreMLExecutionProvider')
                optimal_providers.append('CPUExecutionProvider')
            else:  # Intel Mac
                optimal_providers.append('CPUExecutionProvider')
        elif system == 'linux':
            # Linux 环境，优先 CUDA，然后 CPU
            if 'CUDAExecutionProvider' in available_providers:
                optimal_providers.append('CUDAExecutionProvider')
            if 'TensorrtExecutionProvider' in available_providers:
                optimal_providers.append('TensorrtExecutionProvider')
            optimal_providers.append('CPUExecutionProvider')
        elif system == 'windows':
            # Windows 环境
            if 'DmlExecutionProvider' in available_providers:  # DirectML
                optimal_providers.append('DmlExecutionProvider')
            if 'CUDAExecutionProvider' in available_providers:
                optimal_providers.append('CUDAExecutionProvider')
            optimal_providers.append('CPUExecutionProvider')
        else:
            # 其他平台，使用 CPU
            optimal_providers.append('CPUExecutionProvider')

        logger.info(f"选择的 ONNX Runtime providers: {optimal_providers}")
        return optimal_providers

    def _load_model(self):
        """加载ONNX模型"""
        start_time = time.time()
        try:
            # 配置ONNX Runtime - 智能选择 providers
            providers = self._get_optimal_providers()

            self.session = ort.InferenceSession(self.model_path, providers=providers)
            self._init_vars()

            loading_time = time.time() - start_time
            if logger_instance:
                logger_instance.log_model_loading("FaceDetection", self.model_path, loading_time)

        except Exception as e:
            logger.error(f"人脸检测模型加载失败: {e}")
            raise

    def _init_vars(self):
        """初始化模型变量"""
        input_cfg = self.session.get_inputs()[0]
        input_shape = input_cfg.shape
        if isinstance(input_shape[2], str):
            self.input_size = None
        else:
            self.input_size = tuple(input_shape[2:4][::-1])

        self.input_name = input_cfg.name
        outputs = self.session.get_outputs()
        if len(outputs[0].shape) == 3:
            self.batched = True

        self.output_names = [o.name for o in outputs]
        self.use_kps = False
        self._num_anchors = 1

        if len(outputs) == 6:
            self.fmc = 3
            self._feat_stride_fpn = [8, 16, 32]
            self._num_anchors = 2
        elif len(outputs) == 9:
            self.fmc = 3
            self._feat_stride_fpn = [8, 16, 32]
            self._num_anchors = 2
            self.use_kps = True
        elif len(outputs) == 10:
            self.fmc = 5
            self._feat_stride_fpn = [8, 16, 32, 64, 128]
            self._num_anchors = 1
        elif len(outputs) == 15:
            self.fmc = 5
            self._feat_stride_fpn = [8, 16, 32, 64, 128]
            self._num_anchors = 1
            self.use_kps = True

    def distance2bbox(self, points: np.ndarray, distance: np.ndarray) -> np.ndarray:
        """将距离预测解码为边界框"""
        x1 = points[:, 0] - distance[:, 0]
        y1 = points[:, 1] - distance[:, 1]
        x2 = points[:, 0] + distance[:, 2]
        y2 = points[:, 1] + distance[:, 3]
        return np.stack([x1, y1, x2, y2], axis=-1)

    def distance2kps(self, points: np.ndarray, distance: np.ndarray) -> np.ndarray:
        """将距离预测解码为关键点"""
        preds = []
        for i in range(0, distance.shape[1], 2):
            px = points[:, i % 2] + distance[:, i]
            py = points[:, i % 2 + 1] + distance[:, i + 1]
            preds.append(px)
            preds.append(py)
        return np.stack(preds, axis=-1)
    
    def forward(self, img: np.ndarray, thresh: float) -> Tuple[List, List, List]:
        """前向推理"""
        scores_list = []
        bboxes_list = []
        kpss_list = []

        input_size = tuple(img.shape[0:2][::-1])
        blob = cv2.dnn.blobFromImage(img, 1.0/128, input_size, (127.5, 127.5, 127.5), swapRB=True)
        net_outs = self.session.run(self.output_names, {self.input_name: blob})

        input_height = blob.shape[2]
        input_width = blob.shape[3]
        fmc = self.fmc

        for idx, stride in enumerate(self._feat_stride_fpn):
            # 根据模型是否支持batch维度处理输出
            if self.batched:
                scores = net_outs[idx][0]
                bbox_preds = net_outs[idx + fmc][0]
                bbox_preds = bbox_preds * stride
                if self.use_kps:
                    kps_preds = net_outs[idx + fmc * 2][0] * stride
            else:
                scores = net_outs[idx]
                bbox_preds = net_outs[idx + fmc]
                bbox_preds = bbox_preds * stride
                if self.use_kps:
                    kps_preds = net_outs[idx + fmc * 2] * stride

            height = input_height // stride
            width = input_width // stride
            key = (height, width, stride)

            if key in self.center_cache:
                anchor_centers = self.center_cache[key]
            else:
                # 生成anchor centers
                anchor_centers = np.stack(np.mgrid[:height, :width][::-1], axis=-1).astype(np.float32)
                anchor_centers = (anchor_centers * stride).reshape((-1, 2))
                if self._num_anchors > 1:
                    anchor_centers = np.stack([anchor_centers] * self._num_anchors, axis=1).reshape((-1, 2))
                if len(self.center_cache) < 100:
                    self.center_cache[key] = anchor_centers

            pos_inds = np.where(scores >= thresh)[0]
            bboxes = self.distance2bbox(anchor_centers, bbox_preds)
            pos_scores = scores[pos_inds]
            pos_bboxes = bboxes[pos_inds]
            scores_list.append(pos_scores)
            bboxes_list.append(pos_bboxes)

            if self.use_kps:
                kpss = self.distance2kps(anchor_centers, kps_preds)
                kpss = kpss.reshape((kpss.shape[0], -1, 2))
                pos_kpss = kpss[pos_inds]
                kpss_list.append(pos_kpss)

        return scores_list, bboxes_list, kpss_list

    def detect(self, img: np.ndarray, thresh: float = 0.5, max_num: int = 0) -> Tuple[np.ndarray, np.ndarray]:
        """检测人脸"""
        assert self.input_size is not None

        im_ratio = float(img.shape[0]) / img.shape[1]
        model_ratio = float(self.input_size[1]) / self.input_size[0]

        if im_ratio > model_ratio:
            new_height = self.input_size[1]
            new_width = int(new_height / im_ratio)
        else:
            new_width = self.input_size[0]
            new_height = int(new_width * im_ratio)

        det_scale = float(new_height) / img.shape[0]
        resized_img = cv2.resize(img, (new_width, new_height))
        det_img = np.zeros((self.input_size[1], self.input_size[0], 3), dtype=np.uint8)
        det_img[:new_height, :new_width, :] = resized_img

        scores_list, bboxes_list, kpss_list = self.forward(det_img, thresh)

        scores = np.vstack(scores_list)
        scores_ravel = scores.ravel()
        order = scores_ravel.argsort()[::-1]
        bboxes = np.vstack(bboxes_list) / det_scale

        if self.use_kps:
            kpss = np.vstack(kpss_list) / det_scale

        pre_det = np.hstack((bboxes, scores)).astype(np.float32, copy=False)
        pre_det = pre_det[order, :]
        keep = self._nms(pre_det)
        det = pre_det[keep, :]

        if self.use_kps:
            kpss = kpss[order, :, :]
            kpss = kpss[keep, :, :]
        else:
            kpss = None

        if max_num > 0 and det.shape[0] > max_num:
            area = (det[:, 2] - det[:, 0]) * (det[:, 3] - det[:, 1])
            img_center = img.shape[0] // 2, img.shape[1] // 2
            offsets = np.vstack([
                (det[:, 0] + det[:, 2]) / 2 - img_center[1],
                (det[:, 1] + det[:, 3]) / 2 - img_center[0]
            ])
            offset_dist_squared = np.sum(np.power(offsets, 2.0), 0)
            values = area - offset_dist_squared * 2.0
            bindex = np.argsort(values)[::-1]
            bindex = bindex[0:max_num]
            det = det[bindex, :]
            if kpss is not None:
                kpss = kpss[bindex, :]

        return det, kpss

    def _nms(self, dets: np.ndarray, thresh: float = None) -> np.ndarray:
        """非极大值抑制"""
        if thresh is None:
            thresh = self.nms_threshold

        x1 = dets[:, 0]
        y1 = dets[:, 1]
        x2 = dets[:, 2]
        y2 = dets[:, 3]
        scores = dets[:, 4]

        areas = (x2 - x1 + 1) * (y2 - y1 + 1)
        order = scores.argsort()[::-1]

        keep = []
        while order.size > 0:
            i = order[0]
            keep.append(i)

            xx1 = np.maximum(x1[i], x1[order[1:]])
            yy1 = np.maximum(y1[i], y1[order[1:]])
            xx2 = np.minimum(x2[i], x2[order[1:]])
            yy2 = np.minimum(y2[i], y2[order[1:]])

            w = np.maximum(0.0, xx2 - xx1 + 1)
            h = np.maximum(0.0, yy2 - yy1 + 1)
            inter = w * h

            ovr = inter / (areas[i] + areas[order[1:]] - inter)

            inds = np.where(ovr <= thresh)[0]
            order = order[inds + 1]

        return keep


class FaceRecognitionModel:
    """人脸识别模型"""
    
    def __init__(self, model_path: str, input_size: int = 112):
        self.model_path = model_path
        self.input_size = input_size
        self.session = None
        self._load_model()

    def _get_optimal_providers(self):
        """智能选择最优的 ONNX Runtime providers"""
        import platform

        available_providers = ort.get_available_providers()
        optimal_providers = []

        # 根据平台选择最优 providers
        system = platform.system().lower()
        machine = platform.machine().lower()

        if system == 'darwin':  # macOS
            if 'arm64' in machine or 'aarch64' in machine:  # Apple Silicon (M1/M2/M3)
                # 优先使用 CoreML，然后是 CPU
                if 'CoreMLExecutionProvider' in available_providers:
                    optimal_providers.append('CoreMLExecutionProvider')
                optimal_providers.append('CPUExecutionProvider')
            else:  # Intel Mac
                optimal_providers.append('CPUExecutionProvider')
        elif system == 'linux':
            # Linux 环境，优先 CUDA，然后 CPU
            if 'CUDAExecutionProvider' in available_providers:
                optimal_providers.append('CUDAExecutionProvider')
            optimal_providers.append('CPUExecutionProvider')
        elif system == 'windows':
            # Windows 环境
            if 'DmlExecutionProvider' in available_providers:  # DirectML
                optimal_providers.append('DmlExecutionProvider')
            if 'CUDAExecutionProvider' in available_providers:
                optimal_providers.append('CUDAExecutionProvider')
            optimal_providers.append('CPUExecutionProvider')
        else:
            # 其他平台，使用 CPU
            optimal_providers.append('CPUExecutionProvider')

        return optimal_providers

    def _load_model(self):
        """加载ONNX模型"""
        start_time = time.time()
        try:
            # 配置ONNX Runtime - 使用智能 provider 选择
            providers = self._get_optimal_providers()

            self.session = ort.InferenceSession(self.model_path, providers=providers)
            
            loading_time = time.time() - start_time
            if logger_instance:
                logger_instance.log_model_loading("FaceRecognition", self.model_path, loading_time)
            
        except Exception as e:
            logger.error(f"人脸识别模型加载失败: {e}")
            raise
    
    def preprocess(self, face_image: np.ndarray) -> np.ndarray:
        """预处理人脸图像"""
        # 缩放到指定尺寸
        face_image = cv2.resize(face_image, (self.input_size, self.input_size))
        
        # 归一化
        face_image = face_image.astype(np.float32)
        face_image = (face_image - 127.5) / 127.5
        
        # 转换为CHW格式
        face_image = np.transpose(face_image, (2, 0, 1))
        face_image = np.expand_dims(face_image, axis=0)
        
        return face_image
    
    def extract_feature(self, face_image: np.ndarray) -> np.ndarray:
        """提取人脸特征"""
        # 预处理
        input_data = self.preprocess(face_image)
        
        # 推理
        input_name = self.session.get_inputs()[0].name
        outputs = self.session.run(None, {input_name: input_data})
        
        # 获取特征向量
        feature = outputs[0][0]
        
        # L2归一化
        feature = feature / np.linalg.norm(feature)
        
        return feature


class FaceQualityModel:
    """基于PFLD的人脸质量评估模型"""

    def __init__(self, model_path: str, input_size: int = 112):
        self.model_path = model_path
        self.input_size = input_size
        self.session = None
        self.pose_estimator = None
        # 从150个关键点中选择68个点的索引
        self.points_idx = [0, 1, 2, 3, 75, 4, 5, 77, 6, 78, 7, 8, 80, 9, 10, 11, 12, 84, 23, 24, 25, 85, 86, 40, 41, 42, 87, 108, 109, 110, 57, 50, 112, 115,
                          113, 53, 13, 14, 16, 17, 18, 20, 30, 31, 33, 34, 35, 37, 58, 59, 121, 60, 122, 61, 62, 63, 129, 64, 130, 65, 116, 66, 67, 68, 117, 69, 70, 71]
        self._load_model()

    def _get_optimal_providers(self):
        """智能选择最优的 ONNX Runtime providers"""
        import platform

        available_providers = ort.get_available_providers()
        optimal_providers = []

        # 根据平台选择最优 providers
        system = platform.system().lower()
        machine = platform.machine().lower()

        if system == 'darwin':  # macOS
            if 'arm64' in machine or 'aarch64' in machine:  # Apple Silicon (M1/M2/M3)
                # 优先使用 CoreML，然后是 CPU
                if 'CoreMLExecutionProvider' in available_providers:
                    optimal_providers.append('CoreMLExecutionProvider')
                optimal_providers.append('CPUExecutionProvider')
            else:  # Intel Mac
                optimal_providers.append('CPUExecutionProvider')
        elif system == 'linux':
            # Linux 环境，优先 CUDA，然后 CPU
            if 'CUDAExecutionProvider' in available_providers:
                optimal_providers.append('CUDAExecutionProvider')
            optimal_providers.append('CPUExecutionProvider')
        elif system == 'windows':
            # Windows 环境
            if 'DmlExecutionProvider' in available_providers:  # DirectML
                optimal_providers.append('DmlExecutionProvider')
            if 'CUDAExecutionProvider' in available_providers:
                optimal_providers.append('CUDAExecutionProvider')
            optimal_providers.append('CPUExecutionProvider')
        else:
            # 其他平台，使用 CPU
            optimal_providers.append('CPUExecutionProvider')

        return optimal_providers

    def _load_model(self):
        """加载ONNX模型"""
        start_time = time.time()
        try:
            # 配置ONNX Runtime - 使用智能 provider 选择
            providers = self._get_optimal_providers()

            self.session = ort.InferenceSession(self.model_path, providers=providers)
            self.pose_estimator = PoseEstimator()

            loading_time = time.time() - start_time
            if logger_instance:
                logger_instance.log_model_loading("FaceQuality", self.model_path, loading_time)

        except Exception as e:
            logger.error(f"人脸质量评估模型加载失败: {e}")
            raise

    def preprocess(self, face_image: np.ndarray) -> np.ndarray:
        """预处理人脸图像"""
        # 缩放到指定尺寸
        image = cv2.resize(face_image, (self.input_size, self.input_size))

        # 归一化 - 使用ImageNet标准
        image = image.astype(np.float32)
        image /= 255.0
        image -= np.array([0.485, 0.456, 0.406])
        image /= np.array([0.226, 0.226, 0.226])

        # 转换为CHW格式
        image = np.transpose(image, (2, 0, 1))
        input_data = np.expand_dims(image, 0)

        return input_data

    def assess_quality(self, face_image: np.ndarray) -> Dict[str, float]:
        """评估人脸质量"""
        h, w = face_image.shape[:2]

        # 预处理
        input_data = self.preprocess(face_image)

        # 推理
        output = self.session.run(["output"], {"input": input_data})
        output = output[0][0]

        # 解析输出
        lmk = output[0:300]  # 150个关键点 (x,y)
        cls = output[300:303]  # 分类结果
        blur = output[303:304]  # 模糊度
        illu = output[304:305]  # 光照

        # 处理关键点
        lmk = lmk.reshape(150, 2)
        lmk[:, 0] *= w
        lmk[:, 1] *= h

        # 获取68个关键点用于姿态估计
        points_68 = np.matrix(lmk[self.points_idx])

        # 计算头部姿态
        pose = self.pose_estimator.solve_pose_by_68_points(points_68)
        pitch, yaw, roll = self.pose_estimator.get_euler_angle(pose[0])

        # 计算质量分数
        cls_score = int(np.argmax(cls))  # 转换为Python int
        blur_score = float(blur[0])
        illu_score = float(illu[0])

        # 综合质量评分
        quality_score = self._calculate_overall_quality(cls_score, blur_score, illu_score, pitch, yaw, roll)

        return {
            'quality_score': float(quality_score),
            'blur_score': blur_score,
            'illumination_score': illu_score,
            'classification_score': cls_score,
            'pitch': float(pitch),
            'yaw': float(yaw),
            'roll': float(roll),
            'landmarks': lmk.tolist()
        }

    def _calculate_overall_quality(self, cls_score: int, blur_score: float, illu_score: float,
                                 pitch: float, yaw: float, roll: float) -> float:
        """计算综合质量分数"""
        # 角度惩罚
        angle_penalty = 0.0
        if abs(pitch) > 30:
            angle_penalty += 0.2
        if abs(yaw) > 30:
            angle_penalty += 0.2
        if abs(roll) > 30:
            angle_penalty += 0.1

        # 模糊度和光照评分 (假设值越小越好)
        blur_penalty = max(0, blur_score - 0.5) * 0.3
        illu_penalty = max(0, abs(illu_score - 0.5) - 0.3) * 0.2

        # 基础分数
        base_score = 1.0 if cls_score == 0 else 0.8  # 假设0是最好的分类

        # 计算最终分数
        final_score = base_score - angle_penalty - blur_penalty - illu_penalty

        return max(0.0, min(1.0, final_score))

    def _calculate_quality_score(self, landmarks: np.ndarray) -> float:
        """基于关键点计算质量分数"""
        # 简化的质量评估逻辑
        # 实际应用中可能需要更复杂的算法

        # 检查关键点的对称性
        if len(landmarks) >= 10:
            left_eye = landmarks[0:2]
            right_eye = landmarks[2:4]
            nose = landmarks[4:6]
            left_mouth = landmarks[6:8]
            right_mouth = landmarks[8:10]

            # 计算眼睛间距
            eye_distance = np.linalg.norm(right_eye - left_eye)

            if eye_distance > 0:
                # 计算面部对称性
                face_center = (left_eye + right_eye) / 2
                nose_offset = np.linalg.norm(nose - face_center)
                mouth_center = (left_mouth + right_mouth) / 2
                mouth_offset = np.linalg.norm(mouth_center - face_center)

                # 基于对称性计算质量分数
                symmetry_score = 1.0 - min(1.0, (nose_offset + mouth_offset) / eye_distance)
                return max(0.0, min(1.0, symmetry_score))

        return 0.5  # 默认质量分数

    def _calculate_face_angles(self, landmarks: np.ndarray) -> Dict[str, float]:
        """计算人脸角度"""
        # 简化的角度计算
        if len(landmarks) >= 10:
            left_eye = landmarks[0:2]
            right_eye = landmarks[2:4]
            nose = landmarks[4:6]
            left_mouth = landmarks[6:8]
            right_mouth = landmarks[8:10]

            # 计算roll角度（头部倾斜）
            eye_vector = right_eye - left_eye
            roll = np.arctan2(eye_vector[1], eye_vector[0]) * 180 / np.pi

            # 计算yaw角度（左右转头）
            face_center = (left_eye + right_eye) / 2
            nose_vector = nose - face_center
            yaw = np.arctan2(nose_vector[0], nose_vector[1]) * 180 / np.pi

            # 计算pitch角度（上下点头）
            mouth_center = (left_mouth + right_mouth) / 2
            vertical_vector = mouth_center - face_center
            pitch = np.arctan2(vertical_vector[1], vertical_vector[0]) * 180 / np.pi

            return {
                'pitch': float(pitch),
                'yaw': float(yaw),
                'roll': float(roll)
            }

        return {'pitch': 0.0, 'yaw': 0.0, 'roll': 0.0}


class FaceCropAlign(threading.Thread):
    """基于关键点的人脸裁剪对齐算法 - 源项目核心算法"""

    def __init__(self, image, boxes, landmarks, h, w, transform_mat, padding=0, mean=0, std=1):
        super(FaceCropAlign, self).__init__()
        self.image = image
        self.boxes = boxes
        self.landmarks = landmarks
        self.height = h
        self.width = w
        self.padding = padding
        self.mean = mean
        self.std = std
        # 变换矩阵处理
        self.tran_mat = (np.array(transform_mat, dtype=np.float32).reshape(-1, 2) + padding) / (1 + 2 * padding) * np.array([w, h])
        self.out_0 = list()

    def run(self):
        """执行人脸裁剪对齐"""
        img = self.image
        img_h, img_w, img_c = img.shape

        for box, landmark in zip(self.boxes, self.landmarks):
            shape = self.tran_mat.shape
            landmarkn = landmark[:(shape[0] * shape[1])].reshape((-1, 2))

            # 使用相似变换进行对齐
            st = skimage.transform.SimilarityTransform()
            st.estimate(landmarkn, self.tran_mat)

            # 仿射变换
            crop_img_np = cv2.warpAffine(
                img,
                st.params[0:2, :],
                (self.height, self.width),
                flags=cv2.INTER_LINEAR,
                borderValue=0.0
            )

            # 归一化
            crop_img_np = crop_img_np.astype(np.float32)
            crop_img_np = (crop_img_np - self.mean) / self.std
            crop_img_np = np.transpose(crop_img_np, (2, 0, 1))
            crop_img_np = np.ascontiguousarray(crop_img_np)
            self.out_0.append(crop_img_np)

    def getResult(self):
        return self.out_0


class FaceCropAlignClient:
    """人脸裁剪对齐客户端 - 源项目核心算法"""

    def __init__(self, config):
        self.output_crop_w = 112
        self.output_crop_h = 112
        self.config = config

        # 解析配置参数
        self.mean = config.get('mean', 0)
        self.std = config.get('std', 1)
        self.padding = config.get('padding', 0)
        self.max_batch_size = config.get('max_batch_size', 32)
        self.min_box_size = config.get('min_box_size', [20, 20])
        # 默认的人脸关键点变换矩阵 (5个关键点)
        self.transform_mat = config.get('transform_mat', [[38.2946, 51.6963], [73.5318, 51.5014], [56.0252, 71.7366], [41.5493, 92.3655], [70.7299, 92.2041]])

    def execute(self, images, obj_idxes, landmarks):
        """执行批量人脸裁剪对齐"""
        h = self.output_crop_h
        w = self.output_crop_w

        image_strs = images
        boxes = obj_idxes
        landmarks = landmarks

        out_0_all = list()

        # 过滤小尺寸框
        filter_ids = np.where((boxes[:, 2] > self.min_box_size[0]) & (boxes[:, 3] > self.min_box_size[1]))[0]

        if len(filter_ids) > 0:
            boxes = boxes[filter_ids]
            landmarks = landmarks[filter_ids]
            thread_list = list()

            img_ids = np.unique(boxes[:, -1]).astype(np.int32)
            if image_strs.ndim == 3:
                image_strs = np.expand_dims(image_strs, axis=0)

            for img_id in img_ids:
                img_selected = image_strs[img_id]
                box_ids = np.where(boxes[:, -1] == img_id)[0]
                box_selected = boxes[box_ids]
                landmark_selected = landmarks[box_ids]

                t = FaceCropAlign(
                    img_selected,
                    box_selected[:, :4],
                    landmark_selected,
                    h, w,
                    self.transform_mat,
                    self.padding,
                    self.mean,
                    self.std
                )
                t.start()
                thread_list.append(t)

            for t in thread_list:
                t.join()
                out_0 = t.getResult()
                if len(out_0) > 0:
                    out_0_all.extend(out_0)

            b = self.max_batch_size if len(out_0_all) > self.max_batch_size else len(out_0_all)

            if len(out_0_all) > 0:
                out_0_all_np = np.stack(out_0_all[:b])
                out_0_all_np = np.ascontiguousarray(out_0_all_np)
                out_1_all_np = np.stack(boxes[:b])
                out_1_all_np = np.ascontiguousarray(out_1_all_np)
                out_2_all_np = np.stack(landmarks[:b])
                out_2_all_np = np.ascontiguousarray(out_2_all_np)
            else:
                # 返回空结果
                out_0_all_np = np.zeros((1, 3, h, w))
                out_1_all_np = np.ones((1, 7)) * -1
                out_2_all_np = np.ones((1, 10)) * -1
        else:
            # 返回空结果
            out_0_all_np = np.zeros((1, 3, h, w))
            out_1_all_np = np.ones((1, 7)) * -1
            out_2_all_np = np.ones((1, 10)) * -1

        return out_0_all_np, out_1_all_np


class WenzhouFaceEngine:
    """温州人脸识别主引擎"""

    def __init__(self, config_path: str = "config.ini"):
        self.config = configparser.ConfigParser()
        self.config.read(config_path)

        # 初始化授权管理器
        license_key = self.config.get('LICENSE', 'key')
        self.license_manager = LicenseManager(license_key)

        # 验证授权
        if not self.license_manager.verify_license():
            raise RuntimeError("授权验证失败，请检查license配置")

        # 初始化模型
        self._init_models()

        # 初始化人脸裁剪对齐客户端
        self._init_face_crop_align()

        logger.info("温州人脸识别引擎初始化完成")

    def _init_models(self):
        """初始化所有模型"""
        try:
            # 人脸检测模型
            face_detect_config = self.config['FACE_DETECTION']
            self.face_detector = FaceDetectionModel(
                model_path=face_detect_config.get('model_path'),
                confidence_threshold=face_detect_config.getfloat('confidence_threshold', 0.7),
                nms_threshold=face_detect_config.getfloat('nms_threshold', 0.4),
                input_size=face_detect_config.getint('input_size', 640)
            )

            # 人脸识别模型
            face_recog_config = self.config['FACE_RECOGNITION']
            self.face_recognizer = FaceRecognitionModel(
                model_path=face_recog_config.get('model_path'),
                input_size=face_recog_config.getint('input_size', 112)
            )

            # 人脸质量评估模型（可选）
            quality_enabled = self.config.getboolean('FACE_QUALITY', 'enable_quality_check', fallback=False)
            logger.info(f"质量评估配置: enable_quality_check = {quality_enabled}")

            if quality_enabled:
                face_quality_config = self.config['FACE_QUALITY']
                quality_model_path = face_quality_config.get('model_path')
                logger.info(f"正在加载质量评估模型: {quality_model_path}")

                self.face_quality_assessor = FaceQualityModel(
                    model_path=quality_model_path,
                    input_size=face_quality_config.getint('input_size', 112)
                )
                logger.info("质量评估模型加载完成")
            else:
                logger.info("质量评估功能未启用")
                self.face_quality_assessor = None

        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise

    def _init_face_crop_align(self):
        """初始化人脸裁剪对齐客户端"""
        try:
            # 人脸裁剪对齐配置
            crop_align_config = {
                'mean': 0,
                'std': 1,
                'padding': 0,
                'max_batch_size': 32,
                'min_box_size': [20, 20],
                'transform_mat': [[38.2946, 51.6963], [73.5318, 51.5014], [56.0252, 71.7366], [41.5493, 92.3655], [70.7299, 92.2041]]
            }

            self.face_crop_align_client = FaceCropAlignClient(crop_align_config)

        except Exception as e:
            logger.error(f"人脸裁剪对齐客户端初始化失败: {e}")
            raise

    def crop_face(self, image: np.ndarray, bbox: np.ndarray, landmarks: np.ndarray = None) -> np.ndarray:
        """根据检测框和关键点裁剪人脸 - 使用源项目的对齐算法"""
        if landmarks is not None and len(landmarks) >= 10:
            # 使用关键点进行精确对齐 (源项目算法)
            try:
                # 准备数据格式
                x1, y1, x2, y2 = bbox[:4].astype(int)
                boxes = np.array([[x1, y1, x2 - x1, y2 - y1, 0, 0, 0]])  # x,y,w,h,img_id
                landmarks_flat = landmarks.flatten()
                landmarks_array = np.array([landmarks_flat])

                # 使用FaceCropAlignClient进行对齐
                aligned_faces, _ = self.face_crop_align_client.execute(image, boxes, landmarks_array)

                if len(aligned_faces) > 0 and aligned_faces.shape[0] > 0:
                    # 转换回HWC格式
                    face_image = aligned_faces[0]  # CHW格式
                    face_image = np.transpose(face_image, (1, 2, 0))  # 转换为HWC

                    # 反归一化
                    face_image = face_image * self.face_crop_align_client.std + self.face_crop_align_client.mean
                    face_image = np.clip(face_image, 0, 255).astype(np.uint8)

                    return face_image
            except Exception as e:
                logger.warning(f"关键点对齐失败，使用边界框裁剪: {e}")

        # 回退到简单的边界框裁剪
        x1, y1, x2, y2 = bbox[:4].astype(int)

        # 扩展边界框
        margin = 0.2
        w, h = x2 - x1, y2 - y1
        x1 = max(0, int(x1 - margin * w))
        y1 = max(0, int(y1 - margin * h))
        x2 = min(image.shape[1], int(x2 + margin * w))
        y2 = min(image.shape[0], int(y2 + margin * h))

        # 裁剪人脸
        face_image = image[y1:y2, x1:x2]

        return face_image

    def process_image(self, image: Union[str, np.ndarray],
                     extract_features: bool = True,
                     assess_quality: bool = False) -> Dict:
        """处理单张图像"""
        start_time = time.time()

        # 加载图像
        if isinstance(image, str):
            if logger_instance:
                logger_instance.log_inference_start(image)
            image_array = cv2.imread(image)
            if image_array is None:
                raise ValueError(f"无法加载图像: {image}")
            image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
            image_path = image
        else:
            image_array = image
            image_path = "memory_image"
            if logger_instance:
                logger_instance.log_inference_start(image_path)

        results = {
            'image_path': image_path,
            'faces': [],
            'processing_time': 0.0,
            'num_faces': 0
        }

        try:
            # 人脸检测
            detect_start = time.time()
            bboxes, landmarks = self.face_detector.detect(image_array)
            detect_time = time.time() - detect_start

            if logger_instance:
                logger_instance.log_face_detection(image_path, len(bboxes), detect_time)

            results['num_faces'] = len(bboxes)

            # 处理每个检测到的人脸
            for i, (bbox, landmark) in enumerate(zip(bboxes, landmarks)):
                face_info = {
                    'face_id': i,
                    'bbox': bbox.tolist(),
                    'landmarks': landmark.tolist(),
                    'confidence': float(bbox[4]) if len(bbox) > 4 else 1.0
                }

                # 裁剪人脸
                face_image = self.crop_face(image_array, bbox[:4], landmark)

                # 提取人脸特征
                if extract_features:
                    recog_start = time.time()
                    feature = self.face_recognizer.extract_feature(face_image)
                    recog_time = time.time() - recog_start

                    face_info['feature'] = feature.tolist()
                    face_info['feature_extraction_time'] = recog_time

                # 评估人脸质量
                if assess_quality and self.face_quality_assessor:
                    quality_start = time.time()
                    quality_info = self.face_quality_assessor.assess_quality(face_image)
                    quality_time = time.time() - quality_start

                    face_info['quality'] = quality_info
                    face_info['quality_assessment_time'] = quality_time

                results['faces'].append(face_info)

            # 记录总处理时间
            total_time = time.time() - start_time
            results['processing_time'] = total_time

            if logger_instance:
                logger_instance.log_inference_result(image_path, len(bboxes), total_time)

            return results

        except Exception as e:
            if logger_instance:
                logger_instance.log_error(f"图像处理失败: {image_path}", e)
            raise

    def compare_faces(self, feature1: np.ndarray, feature2: np.ndarray) -> float:
        """比较两个人脸特征的相似度"""
        # 计算余弦相似度
        similarity = np.dot(feature1, feature2) / (np.linalg.norm(feature1) * np.linalg.norm(feature2))
        return float(similarity)

    def process_batch(self, image_paths: List[str],
                     extract_features: bool = True,
                     assess_quality: bool = False) -> List[Dict]:
        """批量处理图像"""
        start_time = time.time()
        results = []
        successful = 0
        failed = 0

        for image_path in image_paths:
            try:
                result = self.process_image(image_path, extract_features, assess_quality)
                results.append(result)
                successful += 1
            except Exception as e:
                logger.error(f"处理图像失败: {image_path}, 错误: {e}")
                results.append({
                    'image_path': image_path,
                    'error': str(e),
                    'faces': [],
                    'num_faces': 0
                })
                failed += 1

        total_time = time.time() - start_time

        if logger_instance:
            logger_instance.log_batch_processing(len(image_paths), successful, failed, total_time)

        return results

    def save_results(self, results: Union[Dict, List[Dict]], output_path: str):
        """保存结果到文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise

    def draw_results(self, image: np.ndarray, faces: List[Dict],
                    draw_bbox: bool = True, draw_landmarks: bool = True) -> np.ndarray:
        """在图像上绘制检测结果"""
        result_image = image.copy()

        for face in faces:
            bbox = face['bbox']
            landmarks = face['landmarks']
            confidence = face.get('confidence', 1.0)

            # 绘制边界框
            if draw_bbox:
                x1, y1, x2, y2 = map(int, bbox[:4])
                cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 绘制置信度
                text = f"Face: {confidence:.2f}"
                cv2.putText(result_image, text, (x1, y1-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

            # 绘制关键点
            if draw_landmarks and landmarks:
                landmarks_array = np.array(landmarks).reshape(-1, 2)
                for point in landmarks_array:
                    cv2.circle(result_image, tuple(map(int, point)), 2, (255, 0, 0), -1)

        return result_image
