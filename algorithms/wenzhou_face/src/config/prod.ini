[LICENSE]
# 请将下面的密钥替换为您从开发者处获得的有效授权密钥
# 示例密钥（仅用于测试）: demo-key-123
key = demo-key-123

[FACE_DETECTION]
# 人脸检测配置参数
model_path = models/scrfd_10g_tykjanimal_240322.onnx
confidence_threshold = 0.7
nms_threshold = 0.4
input_size = 640
max_faces = 100

[FACE_RECOGNITION]
# 人脸识别配置参数
model_path = models/face_feature_Resnet50_zsq_20240201.onnx
feature_dim = 512
similarity_threshold = 0.6
input_size = 112

[FACE_QUALITY]
# 人脸质量评估配置参数
model_path = models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx
enable_quality_check = false
min_quality_score = 0.5
input_size = 112

[PROCESSING]
# 处理配置参数
device = auto
enable_gpu = true
batch_size = 8
num_threads = 4

[OUTPUT]
# 输出配置参数
save_detection_images = true
save_feature_vectors = false
output_format = json
draw_landmarks = true
draw_bboxes = true
