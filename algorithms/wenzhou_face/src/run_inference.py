#!/usr/bin/env python3
"""
温州人脸识别算法 - 客户调用示例
使用说明：
1. 确保已安装所需依赖：pip install onnxruntime opencv-python pillow requests numpy
2. 在 config.ini 中配置您的授权密钥
3. 运行此脚本进行推理测试
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path
from PIL import Image
import cv2
import numpy as np

# 导入推理引擎
try:
    from inference_engine import WenzhouFaceEngine
except ImportError as e:
    print(f"错误：无法导入推理引擎模块: {e}")
    print("请确保 inference_engine.py 文件在同一目录下")
    sys.exit(1)

# 导入日志系统
try:
    from logger_config import get_logger, get_logger_instance
    logger = get_logger()
    logger_instance = get_logger_instance()
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger_instance = None


def validate_image_path(image_path: str) -> bool:
    """验证图像路径"""
    if not os.path.exists(image_path):
        logger.error(f"图像文件不存在: {image_path}")
        return False
    
    # 检查文件扩展名
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    if Path(image_path).suffix.lower() not in valid_extensions:
        logger.error(f"不支持的图像格式: {image_path}")
        return False
    
    return True


def process_single_image(engine: WenzhouFaceEngine, image_path: str, args) -> dict:
    """处理单张图像"""
    print(f"\n🔍 正在处理图像: {image_path}")
    
    if not validate_image_path(image_path):
        return {"error": "图像路径无效"}
    
    try:
        # 执行推理
        results = engine.process_image(
            image_path,
            extract_features=args.extract_features,
            assess_quality=args.assess_quality
        )
        
        # 打印结果摘要
        num_faces = results['num_faces']
        processing_time = results['processing_time']
        
        print(f"✅ 检测完成:")
        print(f"   - 检测到人脸数量: {num_faces}")
        print(f"   - 处理时间: {processing_time:.3f}s")
        
        if num_faces > 0:
            for i, face in enumerate(results['faces']):
                print(f"   - 人脸 {i+1}: 置信度 {face['confidence']:.3f}")
                if 'quality' in face:
                    quality = face['quality']
                    print(f"     质量分数: {quality['quality_score']:.3f}")
                    print(f"     角度: pitch={quality['pitch']:.1f}°, yaw={quality['yaw']:.1f}°, roll={quality['roll']:.1f}°")
        
        # 保存结果
        if args.save_results:
            # 获取项目根目录（wenzhou_face目录）
            script_dir = Path(__file__).parent
            project_root = script_dir.parent
            output_dir = project_root / "data" / "output"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存JSON结果
            json_path = output_dir / f"{Path(image_path).stem}_results.json"
            engine.save_results(results, str(json_path))
            print(f"📄 结果已保存: {json_path}")
            
            # 保存可视化图像
            if args.save_visualization:
                image = cv2.imread(image_path)
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                result_image = engine.draw_results(
                    image_rgb, 
                    results['faces'],
                    draw_bbox=True,
                    draw_landmarks=True
                )
                
                # 转换回BGR用于保存
                result_image_bgr = cv2.cvtColor(result_image, cv2.COLOR_RGB2BGR)
                vis_path = output_dir / f"{Path(image_path).stem}_visualization.jpg"
                cv2.imwrite(str(vis_path), result_image_bgr)
                print(f"🖼️  可视化图像已保存: {vis_path}")
        
        return results
        
    except Exception as e:
        error_msg = f"处理图像时发生错误: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg}


def process_batch_images(engine: WenzhouFaceEngine, input_dir: str, args) -> list:
    """批量处理图像"""
    print(f"\n📁 正在批量处理目录: {input_dir}")
    
    if not os.path.exists(input_dir):
        logger.error(f"输入目录不存在: {input_dir}")
        return []
    
    # 查找所有图像文件
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp']
    image_paths = []
    
    for ext in image_extensions:
        image_paths.extend(Path(input_dir).glob(ext))
        image_paths.extend(Path(input_dir).glob(ext.upper()))
    
    if not image_paths:
        print("❌ 未找到任何图像文件")
        return []
    
    print(f"📊 找到 {len(image_paths)} 个图像文件")
    
    # 批量处理
    image_paths_str = [str(p) for p in image_paths]
    results = engine.process_batch(
        image_paths_str,
        extract_features=args.extract_features,
        assess_quality=args.assess_quality
    )
    
    # 统计结果
    successful = sum(1 for r in results if 'error' not in r)
    failed = len(results) - successful
    total_faces = sum(r.get('num_faces', 0) for r in results)
    
    print(f"✅ 批量处理完成:")
    print(f"   - 成功处理: {successful} 张图像")
    print(f"   - 处理失败: {failed} 张图像")
    print(f"   - 总检测人脸: {total_faces} 张")
    
    # 保存批量结果
    if args.save_results:
        # 获取项目根目录（wenzhou_face目录）
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
        output_dir = project_root / "data" / "output"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        batch_results_path = output_dir / "batch_results.json"
        engine.save_results(results, str(batch_results_path))
        print(f"📄 批量结果已保存: {batch_results_path}")
    
    return results


def compare_two_faces(engine: WenzhouFaceEngine, image1_path: str, image2_path: str) -> dict:
    """比较两张图像中的人脸"""
    print(f"\n🔄 正在比较两张人脸图像:")
    print(f"   图像1: {image1_path}")
    print(f"   图像2: {image2_path}")
    
    try:
        # 处理第一张图像
        results1 = engine.process_image(image1_path, extract_features=True)
        if results1['num_faces'] == 0:
            return {"error": "图像1中未检测到人脸"}
        
        # 处理第二张图像
        results2 = engine.process_image(image2_path, extract_features=True)
        if results2['num_faces'] == 0:
            return {"error": "图像2中未检测到人脸"}
        
        # 获取第一张人脸的特征
        feature1 = np.array(results1['faces'][0]['feature'])
        feature2 = np.array(results2['faces'][0]['feature'])
        
        # 计算相似度
        similarity = engine.compare_faces(feature1, feature2)
        
        # 判断是否为同一人
        threshold = engine.config.getfloat('FACE_RECOGNITION', 'similarity_threshold', fallback=0.6)
        is_same_person = similarity >= threshold
        
        result = {
            'image1': image1_path,
            'image2': image2_path,
            'similarity': float(similarity),
            'threshold': threshold,
            'is_same_person': is_same_person,
            'faces_in_image1': results1['num_faces'],
            'faces_in_image2': results2['num_faces']
        }
        
        print(f"✅ 人脸比较完成:")
        print(f"   - 相似度: {similarity:.4f}")
        print(f"   - 阈值: {threshold}")
        print(f"   - 判断结果: {'同一人' if is_same_person else '不同人'}")
        
        return result
        
    except Exception as e:
        error_msg = f"人脸比较时发生错误: {str(e)}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        return {"error": error_msg}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="温州人脸识别算法 - 推理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 处理单张图像
  python src/run_inference.py data/input/face.jpg --config src/config/dev.ini

  # 批量处理目录
  python src/run_inference.py data/input/ --batch --config src/config/dev.ini

  # 比较两张人脸
  python src/run_inference.py data/input/face1.jpg data/input/face2.jpg --compare --config src/config/dev.ini

  # 包含质量评估和可视化
  python src/run_inference.py data/input/face.jpg --assess-quality --save-visualization --config src/config/dev.ini
        """
    )
    
    parser.add_argument('input', nargs='+', help='输入图像路径或目录')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    parser.add_argument('--compare', action='store_true', help='人脸比较模式（需要两张图像）')
    parser.add_argument('--extract-features', action='store_true', default=True, help='提取人脸特征')
    parser.add_argument('--assess-quality', action='store_true', help='评估人脸质量')
    parser.add_argument('--save-results', action='store_true', default=True, help='保存结果到文件')
    parser.add_argument('--save-visualization', action='store_true', help='保存可视化图像')
    parser.add_argument('--config', default='config.ini', help='配置文件路径')
    
    args = parser.parse_args()
    
    print("🚀 温州人脸识别算法")
    print("=" * 50)
    
    try:
        # 初始化推理引擎
        print("⚙️  正在初始化推理引擎...")
        engine = WenzhouFaceEngine(args.config)
        print("✅ 推理引擎初始化完成")
        
        # 根据模式执行不同操作
        if args.compare:
            if len(args.input) != 2:
                print("❌ 人脸比较模式需要提供两张图像")
                sys.exit(1)
            compare_two_faces(engine, args.input[0], args.input[1])
            
        elif args.batch:
            if len(args.input) != 1:
                print("❌ 批量处理模式只能指定一个目录")
                sys.exit(1)
            process_batch_images(engine, args.input[0], args)
            
        else:
            # 单张图像处理
            for image_path in args.input:
                process_single_image(engine, image_path, args)
        
        print("\n🎉 处理完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
