#!/bin/bash
# 温州人脸识别算法 - 快速测试脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
API_URL=${API_URL:-"http://localhost:8001"}
TIMEOUT=${TIMEOUT:-30}

log_info "🚀 温州人脸识别算法 - 快速测试"
log_info "API地址: $API_URL"

# 等待API服务启动
log_info "等待API服务启动..."
for i in {1..30}; do
    if curl -s "$API_URL/api/v1/health" > /dev/null 2>&1; then
        log_info "✅ API服务已启动"
        break
    fi
    
    if [ $i -eq 30 ]; then
        log_error "❌ API服务启动超时"
        exit 1
    fi
    
    echo -n "."
    sleep 1
done

# 运行测试
log_info "开始运行API测试..."

# 查找测试图像
TEST_IMAGES=""
if [ -d "data/input" ]; then
    TEST_IMAGES=$(find data/input -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | head -2 | tr '\n' ' ')
fi

if [ -z "$TEST_IMAGES" ]; then
    log_warn "未找到测试图像，仅运行基础测试"
    python scripts/test_api.py --url "$API_URL" --test health
    python scripts/test_api.py --url "$API_URL" --test info
else
    log_info "找到测试图像: $TEST_IMAGES"
    python scripts/test_api.py --url "$API_URL" --images $TEST_IMAGES
fi

log_info "🎉 测试完成！"
