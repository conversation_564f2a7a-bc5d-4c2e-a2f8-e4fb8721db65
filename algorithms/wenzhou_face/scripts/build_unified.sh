#!/bin/bash

# 温州人脸识别算法 - 统一构建脚本
# 支持开发和生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "温州人脸识别算法 - 统一构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV     指定环境 (dev|prod，默认: prod)"
    echo "  -t, --tag TAG     指定镜像标签 (默认: wenzhou-face:ENV)"
    echo "  -h, --help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 构建生产镜像"
    echo "  $0 -e dev             # 构建开发镜像"
    echo "  $0 -e prod -t my-tag  # 构建生产镜像并指定标签"
}

# 默认参数
ENV="prod"
TAG=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    print_error "环境参数必须是 'dev' 或 'prod'"
    exit 1
fi

# 设置默认标签
if [[ -z "$TAG" ]]; then
    TAG="wenzhou-face:$ENV"
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALGORITHM_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "开始构建温州人脸识别算法Docker镜像"
print_info "环境: $ENV"
print_info "标签: $TAG"
print_info "根目录: $ALGORITHM_ROOT"

# 切换到算法根目录
cd "$ALGORITHM_ROOT"

# 检查必要文件
required_files=("Dockerfile" "pyproject.toml" "uv.lock" "src/inference_engine.py")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_error "缺少必要文件: $file"
        exit 1
    fi
done

# 构建Docker镜像
print_info "构建Docker镜像..."
if docker build --build-arg ENV="$ENV" -t "$TAG" .; then
    print_success "Docker镜像构建成功: $TAG"
else
    print_error "Docker镜像构建失败"
    exit 1
fi

# 显示镜像信息
print_info "镜像信息:"
docker images "$TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

print_success "构建完成！"
print_info "运行命令: docker run --rm -v \$(pwd)/data:/app/data $TAG"
