#!/bin/bash
# 温州人脸识别算法 - API服务启动脚本

set -e

# 默认配置
HOST=${HOST:-"0.0.0.0"}
PORT=${PORT:-8001}
CONFIG_FILE=${CONFIG_FILE:-"config.ini"}
WORKERS=${WORKERS:-1}
LOG_LEVEL=${LOG_LEVEL:-"info"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Python环境
    if ! command -v python &> /dev/null; then
        log_error "Python未安装"
        exit 1
    fi
    
    # 检查uv包管理器
    if ! command -v uv &> /dev/null; then
        log_error "uv包管理器未安装"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_warn "配置文件 $CONFIG_FILE 不存在，尝试查找其他配置文件..."
        
        # 尝试查找配置文件
        if [ -f "src/config/prod.ini" ]; then
            CONFIG_FILE="src/config/prod.ini"
            log_info "使用配置文件: $CONFIG_FILE"
        elif [ -f "src/config/dev.ini" ]; then
            CONFIG_FILE="src/config/dev.ini"
            log_info "使用配置文件: $CONFIG_FILE"
        else
            log_error "未找到任何配置文件"
            exit 1
        fi
    fi
    
    # 检查模型文件
    if [ ! -d "models" ]; then
        log_error "模型目录不存在"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 检查端口是否可用
check_port() {
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
        log_error "端口 $PORT 已被占用"
        exit 1
    fi
}

# 启动API服务
start_api_server() {
    log_info "启动温州人脸识别API服务器..."
    log_info "配置信息:"
    log_info "  - 主机: $HOST"
    log_info "  - 端口: $PORT"
    log_info "  - 配置文件: $CONFIG_FILE"
    log_info "  - 工作进程: $WORKERS"
    log_info "  - 日志级别: $LOG_LEVEL"
    
    # 设置环境变量
    export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
    
    # 启动服务器
    if [ "$WORKERS" -gt 1 ]; then
        # 多进程模式
        log_info "使用多进程模式启动 (workers: $WORKERS)"
        uv run uvicorn src.api_server:app \
            --host "$HOST" \
            --port "$PORT" \
            --workers "$WORKERS" \
            --log-level "$LOG_LEVEL" \
            --access-log
    else
        # 单进程模式
        log_info "使用单进程模式启动"
        uv run python src/api_server.py \
            --host "$HOST" \
            --port "$PORT" \
            --config "$CONFIG_FILE"
    fi
}

# 开发模式启动
start_dev_server() {
    log_info "启动开发模式服务器 (自动重载)..."
    export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
    
    uv run uvicorn src.api_server:app \
        --host "$HOST" \
        --port "$PORT" \
        --reload \
        --log-level "debug" \
        --access-log
}

# 显示帮助信息
show_help() {
    echo "温州人脸识别算法 - API服务启动脚本"
    echo ""
    echo "用法: $0 [选项] [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动API服务器 (默认)"
    echo "  dev       启动开发模式服务器 (自动重载)"
    echo "  check     检查依赖和配置"
    echo "  help      显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  HOST         服务器主机地址 (默认: 0.0.0.0)"
    echo "  PORT         服务器端口 (默认: 8001)"
    echo "  CONFIG_FILE  配置文件路径 (默认: config.ini)"
    echo "  WORKERS      工作进程数 (默认: 1)"
    echo "  LOG_LEVEL    日志级别 (默认: info)"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  $0 dev"
    echo "  HOST=127.0.0.1 PORT=8080 $0 start"
    echo "  WORKERS=4 $0 start"
}

# 主函数
main() {
    local command=${1:-"start"}
    
    case $command in
        "start")
            check_dependencies
            check_port
            start_api_server
            ;;
        "dev")
            check_dependencies
            check_port
            start_dev_server
            ;;
        "check")
            check_dependencies
            log_info "所有检查通过"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "收到停止信号，正在关闭服务器..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
