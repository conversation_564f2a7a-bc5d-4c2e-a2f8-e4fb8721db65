#!/bin/bash

# 温州人脸识别算法 - 本地开发脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALGORITHM_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "启动温州人脸识别算法本地开发环境"

# 切换到算法根目录
cd "$ALGORITHM_ROOT"

# 确保uv环境
if ! command -v uv &> /dev/null; then
    echo "uv 未安装，请先安装 uv"
    exit 1
fi

# 同步依赖
print_info "同步依赖..."
uv sync

# 设置环境变量
export PYTHONPATH="$ALGORITHM_ROOT/src:$PYTHONPATH"

# 复制开发配置
cp src/config/dev.ini config.ini

print_success "开发环境准备完成！"
print_info "使用方法:"
echo "  uv run python src/run_inference.py --help"
echo "  uv run python src/run_inference.py data/input/sample.jpg"

# 如果提供了参数，直接运行
if [[ $# -gt 0 ]]; then
    print_info "运行: uv run python src/run_inference.py $*"
    uv run python src/run_inference.py "$@"
fi
