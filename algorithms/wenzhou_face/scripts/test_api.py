#!/usr/bin/env python3
"""
温州人脸识别算法 - API测试脚本
用于测试API服务的各个接口功能
"""

import os
import sys
import time
import json
import argparse
import requests
from pathlib import Path
from typing import Dict, List, Optional

# 颜色输出
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color


def log_info(message: str):
    print(f"{Colors.GREEN}[INFO]{Colors.NC} {message}")


def log_warn(message: str):
    print(f"{Colors.YELLOW}[WARN]{Colors.NC} {message}")


def log_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")


def log_success(message: str):
    print(f"{Colors.CYAN}[SUCCESS]{Colors.NC} {message}")


def log_test(message: str):
    print(f"{Colors.PURPLE}[TEST]{Colors.NC} {message}")


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        log_test("测试健康检查接口...")
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/health",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    log_success(f"健康检查通过: {data.get('message')}")
                    return True
                else:
                    log_error(f"健康检查失败: {data.get('message')}")
                    return False
            else:
                log_error(f"健康检查请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            log_error(f"健康检查异常: {e}")
            return False
    
    def test_algorithm_info(self) -> bool:
        """测试算法信息接口"""
        log_test("测试算法信息接口...")
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/info",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    info = data.get('data', {})
                    log_success(f"算法信息获取成功:")
                    log_info(f"  - 算法名称: {info.get('algorithm_name')}")
                    log_info(f"  - 版本: {info.get('version')}")
                    log_info(f"  - 功能: {info.get('capabilities')}")
                    return True
                else:
                    log_error(f"算法信息获取失败: {data.get('message')}")
                    return False
            else:
                log_error(f"算法信息请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            log_error(f"算法信息获取异常: {e}")
            return False
    
    def test_face_detection(self, image_path: str) -> Optional[Dict]:
        """测试人脸检测接口"""
        log_test(f"测试人脸检测接口: {image_path}")
        
        if not os.path.exists(image_path):
            log_error(f"测试图像不存在: {image_path}")
            return None
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
                params = {
                    'extract_features': True,
                    'assess_quality': False,
                    'max_faces': 10
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/v1/detect",
                    files=files,
                    params=params,
                    timeout=self.timeout
                )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('data', {})
                    num_faces = result.get('num_faces', 0)
                    processing_time = result.get('processing_time', 0)
                    
                    log_success(f"人脸检测成功:")
                    log_info(f"  - 检测到人脸数量: {num_faces}")
                    log_info(f"  - 处理时间: {processing_time:.3f}s")
                    
                    if num_faces > 0:
                        for i, face in enumerate(result.get('faces', [])):
                            confidence = face.get('confidence', 0)
                            log_info(f"  - 人脸 {i+1}: 置信度 {confidence:.3f}")
                    
                    return result
                else:
                    log_error(f"人脸检测失败: {data.get('message')}")
                    return None
            else:
                log_error(f"人脸检测请求失败: HTTP {response.status_code}")
                if response.text:
                    log_error(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            log_error(f"人脸检测异常: {e}")
            return None
    
    def test_face_comparison(self, image1_path: str, image2_path: str) -> Optional[Dict]:
        """测试人脸比对接口"""
        log_test(f"测试人脸比对接口: {image1_path} vs {image2_path}")
        
        if not os.path.exists(image1_path):
            log_error(f"第一张图像不存在: {image1_path}")
            return None
        
        if not os.path.exists(image2_path):
            log_error(f"第二张图像不存在: {image2_path}")
            return None
        
        try:
            with open(image1_path, 'rb') as f1, open(image2_path, 'rb') as f2:
                files = {
                    'file1': (os.path.basename(image1_path), f1, 'image/jpeg'),
                    'file2': (os.path.basename(image2_path), f2, 'image/jpeg')
                }
                params = {'threshold': 0.6}
                
                response = self.session.post(
                    f"{self.base_url}/api/v1/compare",
                    files=files,
                    params=params,
                    timeout=self.timeout
                )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('data', {})
                    similarity = result.get('similarity', 0)
                    is_same_person = result.get('is_same_person', False)
                    threshold = result.get('threshold', 0)
                    confidence = result.get('confidence', 'unknown')
                    
                    log_success(f"人脸比对成功:")
                    log_info(f"  - 相似度: {similarity:.4f}")
                    log_info(f"  - 阈值: {threshold}")
                    log_info(f"  - 判断结果: {'同一人' if is_same_person else '不同人'}")
                    log_info(f"  - 置信度: {confidence}")
                    
                    return result
                else:
                    log_error(f"人脸比对失败: {data.get('message')}")
                    return None
            else:
                log_error(f"人脸比对请求失败: HTTP {response.status_code}")
                if response.text:
                    log_error(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            log_error(f"人脸比对异常: {e}")
            return None
    
    def test_face_quality(self, image_path: str) -> Optional[Dict]:
        """测试人脸质量评估接口"""
        log_test(f"测试人脸质量评估接口: {image_path}")
        
        if not os.path.exists(image_path):
            log_error(f"测试图像不存在: {image_path}")
            return None
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
                params = {'min_quality_score': 0.5}
                
                response = self.session.post(
                    f"{self.base_url}/api/v1/quality",
                    files=files,
                    params=params,
                    timeout=self.timeout
                )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('data', {})
                    quality_assessment = result.get('quality_assessment', {})
                    quality_passed = result.get('quality_passed', False)
                    
                    log_success(f"人脸质量评估成功:")
                    log_info(f"  - 质量分数: {quality_assessment.get('quality_score', 0):.3f}")
                    log_info(f"  - 质量合格: {'是' if quality_passed else '否'}")
                    
                    if 'pitch' in quality_assessment:
                        log_info(f"  - 角度: pitch={quality_assessment['pitch']:.1f}°, "
                               f"yaw={quality_assessment['yaw']:.1f}°, "
                               f"roll={quality_assessment['roll']:.1f}°")
                    
                    return result
                else:
                    log_error(f"人脸质量评估失败: {data.get('message')}")
                    return None
            elif response.status_code == 501:
                log_warn("人脸质量评估功能未启用")
                return None
            else:
                log_error(f"人脸质量评估请求失败: HTTP {response.status_code}")
                if response.text:
                    log_error(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            log_error(f"人脸质量评估异常: {e}")
            return None
    
    def run_all_tests(self, test_images: List[str]) -> Dict[str, bool]:
        """运行所有测试"""
        log_info("开始运行API测试套件...")
        
        results = {}
        
        # 1. 健康检查
        results['health_check'] = self.test_health_check()
        
        # 2. 算法信息
        results['algorithm_info'] = self.test_algorithm_info()
        
        # 3. 人脸检测
        if test_images:
            results['face_detection'] = self.test_face_detection(test_images[0]) is not None
            
            # 4. 人脸比对 (需要至少两张图像)
            if len(test_images) >= 2:
                results['face_comparison'] = self.test_face_comparison(test_images[0], test_images[1]) is not None
            
            # 5. 人脸质量评估
            results['face_quality'] = self.test_face_quality(test_images[0]) is not None
        
        # 输出测试结果摘要
        log_info("\n" + "="*50)
        log_info("测试结果摘要:")
        
        passed = 0
        total = len(results)
        
        for test_name, passed_test in results.items():
            status = "✅ 通过" if passed_test else "❌ 失败"
            log_info(f"  {test_name}: {status}")
            if passed_test:
                passed += 1
        
        log_info(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            log_success("🎉 所有测试通过！")
        else:
            log_error(f"❌ {total - passed} 个测试失败")
        
        return results


def main():
    parser = argparse.ArgumentParser(description="温州人脸识别API测试脚本")
    parser.add_argument('--url', default='http://localhost:8001', help='API服务器地址')
    parser.add_argument('--timeout', type=int, default=30, help='请求超时时间(秒)')
    parser.add_argument('--images', nargs='+', help='测试图像路径列表')
    parser.add_argument('--test', choices=['health', 'info', 'detect', 'compare', 'quality', 'all'], 
                       default='all', help='要运行的测试类型')
    
    args = parser.parse_args()
    
    # 查找测试图像
    test_images = args.images or []
    if not test_images:
        # 尝试查找默认测试图像
        possible_paths = [
            'data/input',
            '../data/input',
            'test_images'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                image_files = []
                for ext in ['*.jpg', '*.jpeg', '*.png']:
                    image_files.extend(Path(path).glob(ext))
                if image_files:
                    test_images = [str(f) for f in image_files[:2]]  # 最多取两张
                    break
    
    if not test_images and args.test in ['detect', 'compare', 'quality', 'all']:
        log_warn("未找到测试图像，跳过需要图像的测试")
    
    # 创建测试器
    tester = APITester(args.url, args.timeout)
    
    log_info(f"开始测试API服务: {args.url}")
    
    # 运行指定的测试
    if args.test == 'all':
        tester.run_all_tests(test_images)
    elif args.test == 'health':
        tester.test_health_check()
    elif args.test == 'info':
        tester.test_algorithm_info()
    elif args.test == 'detect' and test_images:
        tester.test_face_detection(test_images[0])
    elif args.test == 'compare' and len(test_images) >= 2:
        tester.test_face_comparison(test_images[0], test_images[1])
    elif args.test == 'quality' and test_images:
        tester.test_face_quality(test_images[0])
    else:
        log_error(f"无法运行测试 '{args.test}': 缺少必要的测试图像")


if __name__ == "__main__":
    main()
