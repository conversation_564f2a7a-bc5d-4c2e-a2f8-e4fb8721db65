#!/bin/bash
# 温州人脸识别算法 - Docker 部署脚本
# 独立脚本，不依赖外部文件

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALGORITHM_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置变量
CONTAINER_NAME="wenzhou-face-detector"
IMAGE_TAG="wenzhou-face:latest"
NETWORK_NAME="wenzhou-face-network"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示用法
show_usage() {
    echo "温州人脸识别算法 - Docker 部署脚本"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --rebuild     重新构建并部署"
    echo "  --stop        停止容器"
    echo "  --start       启动容器"
    echo "  --restart     重启容器"
    echo "  --status      查看容器状态"
    echo "  --logs        查看容器日志"
    echo "  --shell       进入容器shell"
    echo "  --clean       清理容器和网络"
    echo "  -h, --help    显示帮助信息"
}

# 检查 Docker 环境
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装或不在 PATH 中"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行"
        return 1
    fi
    
    return 0
}

# 创建 Docker 网络
create_network() {
    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        print_info "创建 Docker 网络: $NETWORK_NAME"
        docker network create "$NETWORK_NAME"
        print_success "网络创建成功"
    else
        print_info "Docker 网络已存在: $NETWORK_NAME"
    fi
}

# 停止并删除容器
stop_container() {
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        print_info "停止容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        print_success "容器已停止并删除"
    else
        print_info "容器不存在: $CONTAINER_NAME"
    fi
}

# 启动容器
start_container() {
    if docker ps | grep -q "$CONTAINER_NAME"; then
        print_info "容器已在运行: $CONTAINER_NAME"
        return 0
    fi
    
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        print_info "启动现有容器: $CONTAINER_NAME"
        docker start "$CONTAINER_NAME"
        print_success "容器启动成功"
        return 0
    fi
    
    print_error "容器不存在，请先部署: $CONTAINER_NAME"
    return 1
}

# 部署容器
deploy_container() {
    print_info "部署温州人脸识别算法容器..."
    
    # 停止现有容器
    stop_container
    
    # 创建网络
    create_network
    
    # 检查镜像是否存在
    if ! docker images | grep -q "$IMAGE_TAG"; then
        print_error "Docker 镜像不存在: $IMAGE_TAG"
        print_info "请先运行构建脚本: ./scripts/build_docker.sh"
        return 1
    fi
    
    # 创建数据目录
    local data_dir="$ALGORITHM_ROOT/data"
    local logs_dir="$ALGORITHM_ROOT/logs"
    mkdir -p "$data_dir/input" "$data_dir/output" "$logs_dir"
    
    print_info "启动容器: $CONTAINER_NAME"
    
    # 运行容器
    docker run -d \
        --name "$CONTAINER_NAME" \
        --network "$NETWORK_NAME" \
        --restart unless-stopped \
        -v "$data_dir:/app/data" \
        -v "$logs_dir:/app/logs" \
        -e IN_DOCKER=true \
        "$IMAGE_TAG"
    
    # 等待容器启动
    sleep 3
    
    if docker ps | grep -q "$CONTAINER_NAME"; then
        print_success "容器部署成功: $CONTAINER_NAME"
        show_container_status
        return 0
    else
        print_error "容器部署失败"
        print_info "查看容器日志:"
        docker logs "$CONTAINER_NAME" 2>/dev/null || true
        return 1
    fi
}

# 显示容器状态
show_container_status() {
    print_info "容器状态:"
    if docker ps | grep -q "$CONTAINER_NAME"; then
        docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        print_success "容器运行正常"
    elif docker ps -a | grep -q "$CONTAINER_NAME"; then
        docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}"
        print_warning "容器已停止"
    else
        print_warning "容器不存在"
    fi
}

# 查看容器日志
show_container_logs() {
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        print_info "容器日志 (最近50行):"
        docker logs --tail 50 "$CONTAINER_NAME"
    else
        print_error "容器不存在: $CONTAINER_NAME"
    fi
}

# 进入容器shell
enter_container_shell() {
    if docker ps | grep -q "$CONTAINER_NAME"; then
        print_info "进入容器shell: $CONTAINER_NAME"
        docker exec -it "$CONTAINER_NAME" /bin/bash
    else
        print_error "容器未运行: $CONTAINER_NAME"
    fi
}

# 清理资源
clean_resources() {
    print_info "清理温州人脸识别算法相关资源..."
    
    # 停止并删除容器
    stop_container
    
    # 删除网络
    if docker network ls | grep -q "$NETWORK_NAME"; then
        print_info "删除网络: $NETWORK_NAME"
        docker network rm "$NETWORK_NAME" 2>/dev/null || true
    fi
    
    print_success "资源清理完成"
}

# 重新构建并部署
rebuild_and_deploy() {
    print_info "重新构建并部署温州人脸识别算法..."
    
    # 同步代码
    if [ -f "$ALGORITHM_ROOT/scripts/sync_to_docker.sh" ]; then
        print_info "同步代码到Docker目录..."
        bash "$ALGORITHM_ROOT/scripts/sync_to_docker.sh"
    fi
    
    # 构建镜像
    if [ -f "$ALGORITHM_ROOT/scripts/build_docker.sh" ]; then
        print_info "构建Docker镜像..."
        bash "$ALGORITHM_ROOT/scripts/build_docker.sh"
    fi
    
    # 部署容器
    deploy_container
}

# 主函数
main() {
    # 解析参数
    case "${1:-deploy}" in
        --rebuild)
            rebuild_and_deploy
            ;;
        --stop)
            stop_container
            ;;
        --start)
            start_container
            ;;
        --restart)
            stop_container
            sleep 2
            start_container
            ;;
        --status)
            show_container_status
            ;;
        --logs)
            show_container_logs
            ;;
        --shell)
            enter_container_shell
            ;;
        --clean)
            clean_resources
            ;;
        deploy)
            deploy_container
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 检查Docker环境
if ! check_docker; then
    print_error "Docker 环境检查失败"
    exit 1
fi

main "$@"
