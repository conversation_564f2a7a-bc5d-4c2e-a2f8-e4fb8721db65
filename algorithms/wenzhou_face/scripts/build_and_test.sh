#!/bin/bash
# 温州人脸识别算法 - 构建和测试脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 配置
IMAGE_NAME=${IMAGE_NAME:-"wenzhou-face-api"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
CONTAINER_NAME=${CONTAINER_NAME:-"wenzhou-face-test"}
API_PORT=${API_PORT:-8001}
BUILD_ARGS=${BUILD_ARGS:-"--build-arg ENV=prod"}

log_info "🚀 温州人脸识别算法 - 构建和测试"
log_info "镜像名称: $IMAGE_NAME:$IMAGE_TAG"
log_info "容器名称: $CONTAINER_NAME"
log_info "API端口: $API_PORT"

# 清理旧容器
cleanup() {
    log_info "清理旧容器..."
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        docker stop "$CONTAINER_NAME" >/dev/null 2>&1 || true
        docker rm "$CONTAINER_NAME" >/dev/null 2>&1 || true
        log_info "已清理旧容器: $CONTAINER_NAME"
    fi
}

# 构建Docker镜像
build_image() {
    log_step "1. 构建Docker镜像..."
    
    # 检查Dockerfile是否存在
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile不存在"
        exit 1
    fi
    
    # 检查必要文件
    if [ ! -d "src" ]; then
        log_error "src目录不存在"
        exit 1
    fi
    
    if [ ! -d "models" ]; then
        log_error "models目录不存在"
        exit 1
    fi
    
    # 构建镜像
    log_info "开始构建镜像..."
    docker build $BUILD_ARGS -t "$IMAGE_NAME:$IMAGE_TAG" .
    
    if [ $? -eq 0 ]; then
        log_info "✅ 镜像构建成功"
    else
        log_error "❌ 镜像构建失败"
        exit 1
    fi
}

# 启动容器
start_container() {
    log_step "2. 启动容器..."
    
    # 检查端口是否被占用
    if lsof -Pi :$API_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_error "端口 $API_PORT 已被占用"
        exit 1
    fi
    
    # 启动容器
    log_info "启动容器..."
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$API_PORT:8001" \
        -e "IN_DOCKER=1" \
        "$IMAGE_NAME:$IMAGE_TAG"
    
    if [ $? -eq 0 ]; then
        log_info "✅ 容器启动成功"
    else
        log_error "❌ 容器启动失败"
        exit 1
    fi
}

# 等待服务启动
wait_for_service() {
    log_step "3. 等待API服务启动..."
    
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$API_PORT/api/v1/health" >/dev/null 2>&1; then
            log_info "✅ API服务已启动"
            return 0
        fi
        
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    log_error "❌ API服务启动超时"
    
    # 显示容器日志
    log_info "容器日志:"
    docker logs "$CONTAINER_NAME" --tail 50
    
    return 1
}

# 运行测试
run_tests() {
    log_step "4. 运行API测试..."
    
    # 检查测试脚本是否存在
    if [ ! -f "scripts/test_api.py" ]; then
        log_error "测试脚本不存在: scripts/test_api.py"
        return 1
    fi
    
    # 查找测试图像
    local test_images=""
    if [ -d "data/input" ]; then
        test_images=$(find data/input -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | head -2 | tr '\n' ' ')
    fi
    
    # 运行测试
    if [ -n "$test_images" ]; then
        log_info "使用测试图像: $test_images"
        python scripts/test_api.py --url "http://localhost:$API_PORT" --images $test_images
    else
        log_warn "未找到测试图像，仅运行基础测试"
        python scripts/test_api.py --url "http://localhost:$API_PORT" --test health
        python scripts/test_api.py --url "http://localhost:$API_PORT" --test info
    fi
    
    local test_result=$?
    
    if [ $test_result -eq 0 ]; then
        log_info "✅ 所有测试通过"
        return 0
    else
        log_error "❌ 测试失败"
        return 1
    fi
}

# 显示容器信息
show_container_info() {
    log_step "5. 容器信息"
    
    log_info "容器状态:"
    docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    log_info "API访问地址:"
    log_info "  - 健康检查: http://localhost:$API_PORT/api/v1/health"
    log_info "  - API文档: http://localhost:$API_PORT/docs"
    log_info "  - 人脸检测: http://localhost:$API_PORT/api/v1/detect"
    log_info "  - 人脸比对: http://localhost:$API_PORT/api/v1/compare"
    log_info "  - 质量评估: http://localhost:$API_PORT/api/v1/quality"
    
    log_info "容器管理命令:"
    log_info "  - 查看日志: docker logs $CONTAINER_NAME"
    log_info "  - 停止容器: docker stop $CONTAINER_NAME"
    log_info "  - 删除容器: docker rm $CONTAINER_NAME"
}

# 主函数
main() {
    local command=${1:-"all"}
    
    case $command in
        "build")
            build_image
            ;;
        "start")
            cleanup
            start_container
            wait_for_service
            show_container_info
            ;;
        "test")
            run_tests
            ;;
        "stop")
            cleanup
            ;;
        "all")
            cleanup
            build_image
            start_container
            if wait_for_service; then
                run_tests
                show_container_info
            else
                log_error "服务启动失败，跳过测试"
                exit 1
            fi
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  build    仅构建Docker镜像"
            echo "  start    启动容器"
            echo "  test     运行测试"
            echo "  stop     停止并删除容器"
            echo "  all      完整流程 (构建+启动+测试) [默认]"
            echo "  help     显示帮助信息"
            echo ""
            echo "环境变量:"
            echo "  IMAGE_NAME      镜像名称 (默认: wenzhou-face-api)"
            echo "  IMAGE_TAG       镜像标签 (默认: latest)"
            echo "  CONTAINER_NAME  容器名称 (默认: wenzhou-face-test)"
            echo "  API_PORT        API端口 (默认: 8001)"
            echo "  BUILD_ARGS      构建参数 (默认: --build-arg ENV=prod)"
            ;;
        *)
            log_error "未知命令: $command"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "收到停止信号，正在清理..."; cleanup; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
