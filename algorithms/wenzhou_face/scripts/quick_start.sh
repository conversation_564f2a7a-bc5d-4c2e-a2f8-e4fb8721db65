#!/bin/bash
# 温州人脸识别算法 - 快速开始脚本
# 交互式菜单，帮助用户快速上手

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALGORITHM_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_header() {
    echo -e "${PURPLE}$1${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_menu_item() {
    echo -e "${CYAN}$1${NC} $2"
}

# 显示欢迎信息
show_welcome() {
    clear
    print_header "🚀 温州人脸识别算法 - 快速开始"
    print_header "=" * 60
    echo ""
    print_info "欢迎使用温州人脸识别算法！"
    print_info "这是一个基于深度学习的人脸检测、识别和质量评估系统"
    echo ""
    print_info "算法根目录: $ALGORITHM_ROOT"
    echo ""
}

# 显示主菜单
show_main_menu() {
    echo ""
    print_header "📋 请选择操作:"
    echo ""
    print_menu_item "1)" "本地推理测试 - 自定义输入"
    print_menu_item "2)" "本地开发环境 - 快速测试"
    print_menu_item "3)" "Docker 环境 - 完整部署"
    print_menu_item "4)" "系统诊断 - 检查环境和配置"
    print_menu_item "5)" "查看帮助文档"
    print_menu_item "6)" "退出"
    echo ""
}

# 检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    # 检查 uv
    if ! command -v uv &> /dev/null; then
        print_warning "uv 包管理器未安装"
        print_info "建议安装 uv: curl -LsSf https://astral.sh/uv/install.sh | sh"
    fi
    
    # 检查 Docker (可选)
    if ! command -v docker &> /dev/null; then
        print_warning "Docker 未安装 (Docker 部署需要)"
    fi
    
    if [ ${#missing_deps[@]} -eq 0 ]; then
        print_success "基本依赖检查通过"
        return 0
    else
        print_error "缺少依赖: ${missing_deps[*]}"
        return 1
    fi
}

# 检查配置文件
check_config() {
    local config_file="$ALGORITHM_ROOT/src/config/dev.ini"

    if [ ! -f "$config_file" ]; then
        print_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查授权密钥
    local license_key=$(grep "^key" "$config_file" | cut -d'=' -f2 | tr -d ' ')
    
    if [ "$license_key" = "demo-key-123" ]; then
        print_warning "使用的是演示授权密钥"
        print_info "请联系开发者获取正式授权密钥"
    else
        print_success "授权密钥已配置"
    fi
    
    return 0
}

# 检查模型文件
check_models() {
    local models_dir="$ALGORITHM_ROOT/models"

    if [ ! -d "$models_dir" ]; then
        print_error "模型目录不存在: $models_dir"
        return 1
    fi
    
    local required_models=(
        "scrfd_10g_tykjanimal_240322.onnx"
        "face_feature_Resnet50_zsq_20240201.onnx"
        "PFLD_GhostNet_Slim_112_1_opt_20240117.onnx"
    )
    
    local missing_models=()
    
    for model in "${required_models[@]}"; do
        if [ ! -f "$models_dir/$model" ]; then
            missing_models+=("$model")
        fi
    done
    
    if [ ${#missing_models[@]} -eq 0 ]; then
        print_success "模型文件检查通过"
        return 0
    else
        print_error "缺少模型文件: ${missing_models[*]}"
        print_info "请确保模型文件已正确复制到 $models_dir"
        return 1
    fi
}

# 本地推理测试（自定义输入）
local_inference() {
    print_header "🔍 本地推理测试"
    echo ""

    cd "$ALGORITHM_ROOT"

    # 检查并安装依赖
    if command -v uv &> /dev/null; then
        print_info "检查依赖..."
        uv sync --frozen
    else
        print_info "使用 pip 安装依赖..."
        pip install -r requirements.txt
    fi

    # 让用户选择输入方式
    echo ""
    echo "请选择输入方式:"
    echo "  1) 手动输入图像文件路径"
    echo "  2) 使用默认测试图像"
    echo "  3) 批量处理目录"
    echo ""
    read -p "请选择 [1-3]: " input_choice

    local input_file=""
    local extra_args="--config src/config/dev.ini"

    case $input_choice in
        1)
            echo ""
            print_info "请输入图像文件的完整路径:"
            print_info "支持格式: jpg, jpeg, png"
            print_info "示例: /path/to/your/image.jpg"
            print_info "或相对路径: data/input/your_image.jpg"
            echo ""
            read -p "图像路径: " input_file

            # 检查文件是否存在
            if [ ! -f "$input_file" ]; then
                print_error "文件不存在: $input_file"
                return 1
            fi

            # 检查文件格式
            input_file_lower=$(echo "$input_file" | tr '[:upper:]' '[:lower:]')
            case "$input_file_lower" in
                *.jpg|*.jpeg|*.png)
                    print_success "文件格式有效: $input_file"
                    ;;
                *)
                    print_error "不支持的文件格式，请使用 jpg, jpeg 或 png 格式"
                    return 1
                    ;;
            esac

            # 询问是否需要额外功能
            echo ""
            echo "请选择处理选项:"
            echo "  1) 仅人脸检测"
            echo "  2) 人脸检测 + 特征提取"
            echo "  3) 人脸检测 + 特征提取 + 质量评估"
            echo "  4) 完整处理 + 可视化"
            echo ""
            read -p "请选择 [1-4]: " process_choice

            case $process_choice in
                1) extra_args="$extra_args" ;;
                2) extra_args="$extra_args --extract-features" ;;
                3) extra_args="$extra_args --extract-features --assess-quality" ;;
                4) extra_args="$extra_args --extract-features --assess-quality --save-visualization" ;;
                *) extra_args="$extra_args --extract-features --assess-quality" ;;
            esac
            ;;
        2)
            # 查找默认测试图像
            print_info "查找默认测试图像..."
            mkdir -p "data/input"

            for img in "data/input"/*.{jpg,png,jpeg}; do
                if [ -f "$img" ]; then
                    input_file="$img"
                    break
                fi
            done

            if [ -z "$input_file" ]; then
                print_error "未找到默认测试图像"
                print_info "请将测试图像放入 data/input/ 目录"
                return 1
            fi

            print_success "找到默认测试图像: $input_file"
            extra_args="$extra_args --extract-features --assess-quality --save-visualization"
            ;;
        3)
            echo ""
            print_info "请输入要批量处理的目录路径:"
            print_info "示例: /path/to/images/ 或 data/input/"
            echo ""
            read -p "目录路径: " input_dir

            # 检查目录是否存在
            if [ ! -d "$input_dir" ]; then
                print_error "目录不存在: $input_dir"
                return 1
            fi

            # 检查目录中是否有图像文件
            image_count=$(find "$input_dir" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \) | wc -l)
            if [ "$image_count" -eq 0 ]; then
                print_error "目录中没有找到图像文件: $input_dir"
                return 1
            fi

            print_success "找到 $image_count 个图像文件"
            input_file="$input_dir"
            extra_args="$extra_args --batch --extract-features --assess-quality --save-visualization"
            ;;
        *)
            print_error "无效选择"
            return 1
            ;;
    esac

    echo ""
    print_info "开始执行推理..."
    print_info "输入: $input_file"
    print_info "参数: $extra_args"

    # 执行推理
    if command -v uv &> /dev/null; then
        uv run python src/run_inference.py "$input_file" $extra_args
    else
        python src/run_inference.py "$input_file" $extra_args
    fi

    echo ""
    print_success "推理完成！"

    # 显示输出结果
    if [ -d "data/output" ] && [ "$(ls -A data/output 2>/dev/null)" ]; then
        print_info "结果保存在 data/output/ 目录:"
        ls -la data/output/ | tail -10
    else
        print_warning "输出目录为空，请检查推理是否成功"
    fi
}

# 本地开发环境测试
local_development() {
    print_header "🔧 本地开发环境测试"
    echo ""

    cd "$ALGORITHM_ROOT"

    # 检查并安装依赖
    if command -v uv &> /dev/null; then
        print_info "使用 uv 安装依赖..."
        uv sync --frozen
    else
        print_info "使用 pip 安装依赖..."
        pip install -r requirements.txt
    fi
    
    # 创建测试图像目录
    mkdir -p "data/input"

    # 检查是否有测试图像
    if [ ! "$(ls -A data/input)" ]; then
        print_warning "输入目录为空: data/input"
        print_info "请将测试图像放入该目录"
        read -p "按回车键继续..."
        return
    fi
    
    # 运行测试
    print_info "运行人脸识别测试..."

    if command -v uv &> /dev/null; then
        uv run python src/run_inference.py data/input/ --batch --config src/config/dev.ini --assess-quality --save-visualization
    else
        python src/run_inference.py data/input/ --batch --config src/config/dev.ini --assess-quality --save-visualization
    fi
    
    print_success "本地测试完成！"
    print_info "结果保存在: ../data/output/"
}

# Docker 推理
run_docker_inference() {
    print_header "🐳 Docker 推理测试"
    echo ""

    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 未运行，请先启动 Docker"
        return 1
    fi

    # 检查容器是否存在
    local container_name="wenzhou-face-detector"
    if ! docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        print_error "容器 ${container_name} 不存在"
        print_info "请先运行: 选项 2) 完整部署"
        return 1
    fi

    # 检查容器是否运行
    if ! docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        print_info "启动容器 ${container_name}..."
        docker start "${container_name}"
        sleep 2
    fi

    cd "$ALGORITHM_ROOT"

    # 让用户选择输入方式
    echo ""
    echo "请选择输入方式:"
    echo "  1) 手动输入图像文件路径"
    echo "  2) 使用默认测试图像"
    echo "  3) 批量处理目录"
    echo ""
    read -p "请选择 [1-3]: " input_choice

    local input_file=""
    local docker_path=""
    local extra_args=""

    case $input_choice in
        1)
            echo ""
            print_info "请输入图像文件的完整路径:"
            print_info "支持格式: jpg, jpeg, png"
            print_info "示例: /path/to/your/image.jpg"
            print_info "或相对路径: data/input/your_image.jpg"
            echo ""
            read -p "图像路径: " input_file

            # 检查文件是否存在
            if [ ! -f "$input_file" ]; then
                print_error "文件不存在: $input_file"
                return 1
            fi

            # 检查文件格式
            input_file_lower=$(echo "$input_file" | tr '[:upper:]' '[:lower:]')
            case "$input_file_lower" in
                *.jpg|*.jpeg|*.png)
                    print_success "文件格式有效: $input_file"
                    ;;
                *)
                    print_error "不支持的文件格式，请使用 jpg, jpeg 或 png 格式"
                    return 1
                    ;;
            esac

            # 询问是否需要额外功能
            echo ""
            echo "请选择处理选项:"
            echo "  1) 仅人脸检测"
            echo "  2) 人脸检测 + 特征提取"
            echo "  3) 人脸检测 + 特征提取 + 质量评估"
            echo "  4) 完整处理 + 可视化"
            echo ""
            read -p "请选择 [1-4]: " process_choice

            case $process_choice in
                1) extra_args="" ;;
                2) extra_args="--extract-features" ;;
                3) extra_args="--extract-features --assess-quality" ;;
                4) extra_args="--extract-features --assess-quality --save-visualization" ;;
                *) extra_args="--extract-features --assess-quality" ;;
            esac

            # 转换为容器内路径
            if [[ "$input_file" == /* ]]; then
                # 绝对路径，需要挂载
                docker_path="/tmp/input/$(basename "$input_file")"
                docker cp "$input_file" "${container_name}:/tmp/input/"
            else
                # 相对路径，假设在项目目录内
                docker_path="/app/$input_file"
            fi
            ;;
        2)
            # 查找默认测试图像
            print_info "查找默认测试图像..."

            for img in "data/input"/*.{jpg,png,jpeg}; do
                if [ -f "$img" ]; then
                    input_file="$img"
                    break
                fi
            done

            if [ -z "$input_file" ]; then
                print_error "未找到默认测试图像"
                print_info "请将测试图像放入 data/input/ 目录"
                return 1
            fi

            print_success "找到默认测试图像: $input_file"
            docker_path="/app/$input_file"
            extra_args="--extract-features --assess-quality --save-visualization"
            ;;
        3)
            echo ""
            print_info "请输入要批量处理的目录路径:"
            print_info "示例: /path/to/images/ 或 data/input/"
            echo ""
            read -p "目录路径: " input_dir

            # 检查目录是否存在
            if [ ! -d "$input_dir" ]; then
                print_error "目录不存在: $input_dir"
                return 1
            fi

            # 检查目录中是否有图像文件
            image_count=$(find "$input_dir" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \) | wc -l)
            if [ "$image_count" -eq 0 ]; then
                print_error "目录中没有找到图像文件: $input_dir"
                return 1
            fi

            print_success "找到 $image_count 个图像文件"
            input_file="$input_dir"
            extra_args="--batch --extract-features --assess-quality"

            # 转换为容器内路径
            if [[ "$input_file" == /* ]]; then
                # 绝对路径，需要挂载
                docker_path="/tmp/input/"
                docker cp "$input_file/." "${container_name}:/tmp/input/"
            else
                # 相对路径，假设在项目目录内
                docker_path="/app/$input_file"
            fi
            ;;
        *)
            print_error "无效选择"
            return 1
            ;;
    esac

    echo ""
    print_info "开始执行 Docker 推理..."
    print_info "容器: $container_name"
    print_info "输入: $docker_path"
    print_info "参数: $extra_args"

    # 执行推理
    if [ "$input_choice" = "3" ]; then
        # 批量处理
        print_info "批量处理图像文件..."
        docker exec "$container_name" python src/run_inference.py "$docker_path" --config src/config/prod.ini $extra_args
    else
        # 单个图像文件
        docker exec "$container_name" python src/run_inference.py "$docker_path" --config src/config/prod.ini $extra_args
    fi

    echo ""
    print_success "Docker 推理完成！"

    # 显示输出结果
    print_info "检查容器内输出结果..."
    docker exec "$container_name" ls -la /app/data/output/ 2>/dev/null || print_warning "无法访问容器输出目录"
}

# Docker 环境部署
docker_deployment() {
    print_header "🐳 Docker 环境部署"
    echo ""
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装"
        print_info "请先安装 Docker: https://docs.docker.com/get-docker/"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行"
        print_info "请启动 Docker 服务"
        return 1
    fi
    
    print_info "Docker 环境检查通过"
    echo ""
    
    # 显示 Docker 操作菜单
    while true; do
        print_header "Docker 操作菜单:"
        echo ""
        print_menu_item "1)" "运行 Docker 推理"
        print_menu_item "2)" "完整部署 (同步代码 + 构建镜像 + 启动容器)"
        print_menu_item "3)" "仅同步代码到 Docker 目录"
        print_menu_item "4)" "仅构建 Docker 镜像"
        print_menu_item "5)" "仅启动容器"
        print_menu_item "6)" "查看容器状态"
        print_menu_item "7)" "查看容器日志"
        print_menu_item "8)" "进入容器 Shell"
        print_menu_item "9)" "停止容器"
        print_menu_item "10)" "返回主菜单"
        echo ""

        read -p "请选择操作 [1-10]: " docker_choice
        
        case $docker_choice in
            1)
                run_docker_inference
                ;;
            2)
                print_info "执行完整部署..."
                bash "$ALGORITHM_ROOT/scripts/deploy.sh" --rebuild
                ;;
            3)
                print_info "同步代码..."
                bash "$ALGORITHM_ROOT/scripts/sync_to_docker.sh"
                ;;
            4)
                print_info "构建镜像..."
                bash "$ALGORITHM_ROOT/scripts/build_docker.sh"
                ;;
            5)
                print_info "启动容器..."
                bash "$ALGORITHM_ROOT/scripts/deploy.sh"
                ;;
            6)
                bash "$ALGORITHM_ROOT/scripts/deploy.sh" --status
                ;;
            7)
                bash "$ALGORITHM_ROOT/scripts/deploy.sh" --logs
                ;;
            8)
                bash "$ALGORITHM_ROOT/scripts/deploy.sh" --shell
                ;;
            9)
                bash "$ALGORITHM_ROOT/scripts/deploy.sh" --stop
                ;;
            10)
                break
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 系统诊断
system_diagnosis() {
    print_header "🔍 系统诊断"
    echo ""
    
    print_info "正在检查系统环境和配置..."
    echo ""
    
    # 检查依赖
    print_header "1. 依赖检查"
    check_dependencies
    echo ""
    
    # 检查配置
    print_header "2. 配置检查"
    check_config
    echo ""
    
    # 检查模型
    print_header "3. 模型文件检查"
    check_models
    echo ""
    
    # 检查目录结构
    print_header "4. 目录结构检查"
    local required_dirs=(
        "src"
        "scripts"
        "models"
        "data/input"
        "data/output"
        "logs"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$ALGORITHM_ROOT/$dir" ]; then
            print_success "目录存在: $dir"
        else
            print_warning "目录不存在: $dir"
        fi
    done
    
    echo ""
    print_success "系统诊断完成！"
}

# 显示帮助文档
show_help() {
    print_header "📚 帮助文档"
    echo ""
    
    print_info "温州人脸识别算法使用指南:"
    echo ""
    
    print_header "🔧 本地开发"
    echo "1. 进入项目目录: cd algorithms/wenzhou_face"
    echo "2. 安装依赖: uv sync"
    echo "3. 配置授权密钥: 编辑 src/config/dev.ini 文件"
    echo "4. 运行推理: uv run python src/run_inference.py data/input/image.jpg --config src/config/dev.ini"
    echo ""
    
    print_header "🐳 Docker 部署"
    echo "1. 同步代码: ./scripts/sync_to_docker.sh"
    echo "2. 构建镜像: ./scripts/build_docker.sh"
    echo "3. 部署容器: ./scripts/deploy.sh"
    echo "4. 运行推理: docker exec wenzhou-face-detector python run_inference.py /app/data/input/image.jpg"
    echo ""
    
    print_header "📁 目录说明"
    echo "- src/: 源代码目录"
    echo "- models/: 模型文件目录"
    echo "- scripts/: 自动化脚本"
    echo "- data/input/: 输入图像目录"
    echo "- data/output/: 输出结果目录"
    echo "- logs/: 日志文件目录"
    echo ""

    print_header "🔑 授权配置"
    echo "请在 src/config/dev.ini 中配置您的授权密钥:"
    echo "[LICENSE]"
    echo "key = YOUR_LICENSE_KEY"
    echo ""
    
    print_info "更多信息请查看 README.md 文件"
}

# 主循环
main() {
    show_welcome
    
    while true; do
        show_main_menu
        read -p "请选择操作 [1-6]: " choice

        case $choice in
            1)
                local_inference
                ;;
            2)
                local_development
                ;;
            3)
                docker_deployment
                ;;
            4)
                system_diagnosis
                ;;
            5)
                show_help
                ;;
            6)
                print_success "感谢使用温州人脸识别算法！"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

main "$@"
