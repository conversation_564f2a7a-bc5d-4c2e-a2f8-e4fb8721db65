2025-07-24 22:42:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face8.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:<PERSON><PERSON><PERSON> failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face8.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face1.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face1.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face3.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face3.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face7.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face7.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face6.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face6.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face4.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face4.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face5.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face5.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face8.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face8.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face1.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face1.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face3.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face3.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face7.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face7.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face6.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face6.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face4.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face4.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face5.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face5.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 23:03:02 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face1.jpg: 'FaceDetectionModel' object has no attribute 'preprocess'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 901, in process_image
    bboxes, landmarks = self.face_detector.detect(image_array)
                        ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 336, in detect
    input_data, scale = self.preprocess(image)
                        ^^^^^^^^^^^^^^^
AttributeError: 'FaceDetectionModel' object has no attribute 'preprocess'
2025-07-24 23:03:02 | wenzhou_face | ERROR | run_inference.py:117 | 处理图像时发生错误: 'FaceDetectionModel' object has no attribute 'preprocess'
