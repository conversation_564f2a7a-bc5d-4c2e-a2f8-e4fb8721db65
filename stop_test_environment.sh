#!/bin/bash

# 停止测试环境脚本

echo "🛑 停止算法管理平台测试环境"

# 停止Docker容器
if [ -f .container.name ]; then
    CONTAINER_NAME=$(cat .container.name)
    echo "🐳 停止Docker容器: $CONTAINER_NAME"
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    rm .container.name
    echo "✅ Docker容器已停止"
fi

# 停止后端服务
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    echo "🔧 停止后端服务 (PID: $BACKEND_PID)"
    kill $BACKEND_PID 2>/dev/null || true
    rm .backend.pid
    echo "✅ 后端服务已停止"
fi

# 停止前端服务
if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    echo "🎨 停止前端服务 (PID: $FRONTEND_PID)"
    kill $FRONTEND_PID 2>/dev/null || true
    rm .frontend.pid
    echo "✅ 前端服务已停止"
fi

# 清理可能残留的进程
echo "🧹 清理残留进程..."
pkill -f "python src/main.py" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true

echo "🎉 测试环境已完全停止"
