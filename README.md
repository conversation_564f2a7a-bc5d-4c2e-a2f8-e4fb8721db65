# License Server - 极简授权API服务器

一个极简的授权API服务器，提供license密钥验证服务。

## 功能特性

- ✅ 简单的license验证API
- ✅ 自动生成的API文档
- ✅ 健康检查端点
- ✅ CORS支持
- ✅ 错误处理和日志
- ✅ 支持多种部署方式

## 快速开始

### 本地开发

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行服务器：
```bash
python app.py
```

3. 访问API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 使用uv（推荐）

```bash
# 安装依赖
uv sync

# 运行服务器
uv run python app.py
```

## API端点

### 1. 验证License - POST /verify

验证license密钥是否有效。

**请求体：**
```json
{
  "license_key": "demo-key-123"
}
```

**响应：**
```json
{
  "status": "valid",
  "message": "License有效",
  "expires_at": "2025-12-31T23:59:59"
}
```

**状态码：**
- `200`: 请求成功（无论license是否有效）
- `422`: 请求格式错误
- `500`: 服务器内部错误

### 2. 健康检查 - GET /health

检查服务器状态。

**响应：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "data_file_exists": true
}
```

### 3. 服务信息 - GET /

获取服务基本信息。

## 测试示例

### 使用curl测试

```bash
# 测试有效的license
curl -X POST "http://localhost:8000/verify" \
     -H "Content-Type: application/json" \
     -d '{"license_key": "demo-key-123"}'

# 测试无效的license
curl -X POST "http://localhost:8000/verify" \
     -H "Content-Type: application/json" \
     -d '{"license_key": "invalid-key"}'

# 健康检查
curl "http://localhost:8000/health"
```

### 使用Python测试

```python
import requests

# 验证license
response = requests.post(
    "http://localhost:8000/verify",
    json={"license_key": "demo-key-123"}
)
print(response.json())
```

## 数据管理

License数据存储在 `license_data.json` 文件中：

```json
{
  "licenses": {
    "your-license-key": {
      "expires_at": "2025-12-31T23:59:59",
      "description": "密钥描述"
    }
  }
}
```

### 添加新的License

直接编辑 `license_data.json` 文件，添加新的密钥：

```json
{
  "licenses": {
    "new-key-001": {
      "expires_at": "2025-12-31T23:59:59",
      "description": "新客户密钥"
    }
  }
}
```

## 部署选项

### 1. 传统VPS部署

```bash
# 克隆项目
git clone <your-repo>
cd license-server

# 安装依赖
pip install -r requirements.txt

# 生产环境运行
uvicorn app:app --host 0.0.0.0 --port 8000
```

### 2. Docker部署

创建 `Dockerfile`：
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]
```

运行：
```bash
docker build -t license-server .
docker run -p 8000:8000 license-server
```

### 3. Vercel部署

创建 `vercel.json`：
```json
{
  "builds": [
    {
      "src": "app.py",
      "use": "@vercel/python"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "app.py"
    }
  ]
}
```

### 4. Railway/Render部署

这些平台通常会自动检测Python项目并使用 `requirements.txt`。

## 安全建议

1. **生产环境配置**：
   - 限制CORS域名
   - 使用HTTPS
   - 添加API密钥认证
   - 限制请求频率

2. **数据安全**：
   - 定期备份license数据
   - 考虑使用数据库替代JSON文件
   - 加密敏感信息

3. **监控**：
   - 添加日志记录
   - 监控API使用情况
   - 设置告警

## 扩展功能

可以考虑添加的功能：
- 管理API（添加/删除/更新license）
- 使用统计和分析
- 多租户支持
- 数据库集成
- 缓存机制

## 许可证

MIT License
