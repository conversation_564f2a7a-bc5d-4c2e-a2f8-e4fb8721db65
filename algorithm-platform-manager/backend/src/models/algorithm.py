"""
算法相关的数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class AlgorithmStatus(str, Enum):
    """算法状态枚举"""
    UPLOADED = "uploaded"           # 已上传
    BUILDING = "building"           # 构建中
    BUILT = "built"                # 已构建
    BUILD_FAILED = "build_failed"   # 构建失败
    STARTING = "starting"           # 启动中
    RUNNING = "running"             # 运行中
    STOPPING = "stopping"           # 停止中
    STOPPED = "stopped"             # 已停止
    START_FAILED = "start_failed"   # 启动失败
    UNHEALTHY = "unhealthy"         # 不健康
    ERROR = "error"                 # 错误状态


class ContainerInfo(BaseModel):
    """容器信息"""
    container_id: str = Field(..., description="容器ID")
    container_name: str = Field(..., description="容器名称")
    host_port: int = Field(..., description="主机端口")
    api_base_url: str = Field(..., description="API基础URL")
    health_check_url: str = Field(..., description="健康检查URL")
    created_at: Optional[datetime] = Field(default_factory=datetime.now, description="创建时间")


class Algorithm(BaseModel):
    """算法模型"""
    name: str = Field(..., description="算法名称")
    version: str = Field(default="1.0.0", description="算法版本")
    description: str = Field(default="", description="算法描述")
    docker_image: str = Field(..., description="Docker镜像名称")
    api_port: int = Field(default=8001, description="容器内API端口")
    status: AlgorithmStatus = Field(default=AlgorithmStatus.UPLOADED, description="算法状态")
    capabilities: List[str] = Field(default_factory=list, description="算法能力列表")
    input_formats: List[str] = Field(default_factory=lambda: ["jpg", "png"], description="支持的输入格式")
    package_path: Optional[str] = Field(None, description="算法包路径")
    container_info: Optional[ContainerInfo] = Field(None, description="容器信息")
    created_at: Optional[datetime] = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        use_enum_values = True


class AlgorithmUploadRequest(BaseModel):
    """算法上传请求"""
    filename: str = Field(..., description="文件名")
    description: Optional[str] = Field(None, description="算法描述")


class AlgorithmResponse(BaseModel):
    """算法响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class AlgorithmListResponse(BaseModel):
    """算法列表响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    algorithms: List[Dict[str, Any]] = Field(..., description="算法列表")
    total: int = Field(..., description="总数量")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class AlgorithmStatusResponse(BaseModel):
    """算法状态响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    algorithm: Dict[str, Any] = Field(..., description="算法信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    results: Dict[str, Any] = Field(..., description="检查结果")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class AlgorithmLogsResponse(BaseModel):
    """算法日志响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    logs: List[str] = Field(..., description="日志内容")
    algorithm_name: str = Field(..., description="算法名称")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class APIProxyRequest(BaseModel):
    """API代理请求"""
    algorithm_name: str = Field(..., description="算法名称")
    endpoint: str = Field(..., description="API端点")
    method: str = Field(default="POST", description="HTTP方法")
    headers: Optional[Dict[str, str]] = Field(default_factory=dict, description="请求头")
    params: Optional[Dict[str, Any]] = Field(default_factory=dict, description="查询参数")


class APIProxyResponse(BaseModel):
    """API代理响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    status_code: int = Field(..., description="HTTP状态码")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    headers: Optional[Dict[str, str]] = Field(None, description="响应头")
    processing_time: Optional[float] = Field(None, description="处理时间")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class SystemConfig(BaseModel):
    """系统配置模型"""
    max_algorithms: int = Field(default=10, description="最大算法数量")
    default_timeout: int = Field(default=30, description="默认超时时间(秒)")
    log_level: str = Field(default="INFO", description="日志级别")
    data_retention_days: int = Field(default=30, description="数据保留天数")
    auto_cleanup: bool = Field(default=True, description="自动清理")
    
    class Config:
        use_enum_values = True


class SystemConfigResponse(BaseModel):
    """系统配置响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    config: SystemConfig = Field(..., description="系统配置")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")





class TaskInfo(BaseModel):
    """任务信息"""
    task_id: str = Field(..., description="任务ID")
    algorithm_name: str = Field(..., description="算法名称")
    endpoint: str = Field(..., description="API端点")
    status: str = Field(..., description="任务状态")
    input_data: Optional[Dict[str, Any]] = Field(None, description="输入数据")
    output_data: Optional[Dict[str, Any]] = Field(None, description="输出数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    processing_time: Optional[float] = Field(None, description="处理时间")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class TaskResponse(BaseModel):
    """任务响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    task: TaskInfo = Field(..., description="任务信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class TaskListResponse(BaseModel):
    """任务列表响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    tasks: List[TaskInfo] = Field(..., description="任务列表")
    total: int = Field(..., description="总数量")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class PlatformStats(BaseModel):
    """平台统计信息"""
    total_algorithms: int = Field(..., description="总算法数量")
    running_algorithms: int = Field(..., description="运行中算法数量")
    total_tasks: int = Field(..., description="总任务数量")
    successful_tasks: int = Field(..., description="成功任务数量")
    failed_tasks: int = Field(..., description="失败任务数量")
    system_uptime: str = Field(..., description="系统运行时间")
    memory_usage: float = Field(..., description="内存使用率")
    cpu_usage: float = Field(..., description="CPU使用率")
    disk_usage: float = Field(..., description="磁盘使用率")


class PlatformStatsResponse(BaseModel):
    """平台统计响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    stats: PlatformStats = Field(..., description="统计信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
