"""
系统配置和授权管理API路由
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging
import psutil
import time
from datetime import datetime

from ..core.auth_manager import AuthManager
from ..core.database import DatabaseManager
from ..models.algorithm import (
    SystemConfig, SystemConfigResponse, LicenseVerificationRequest,
    LicenseVerificationResponse, PlatformStats, PlatformStatsResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/system", tags=["系统管理"])

# 全局实例
auth_manager = AuthManager()
db_manager = DatabaseManager()

# 记录系统启动时间
system_start_time = time.time()


@router.get("/config", response_model=SystemConfigResponse)
async def get_system_config():
    """获取系统配置"""
    try:
        config = auth_manager.get_current_config()
        
        # 隐藏敏感信息
        safe_config = config.copy()
        if safe_config.license_key:
            safe_config.license_key = "***" + safe_config.license_key[-4:] if len(safe_config.license_key) > 4 else "***"
        
        return SystemConfigResponse(
            success=True,
            message="系统配置获取成功",
            config=safe_config
        )
        
    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统配置失败: {str(e)}")


@router.post("/config", response_model=SystemConfigResponse)
async def update_system_config(config: SystemConfig):
    """更新系统配置"""
    try:
        # 保存配置
        success = db_manager.save_system_config(config)
        
        if success:
            # 刷新授权管理器配置
            await auth_manager.refresh_config()
            
            # 隐藏敏感信息
            safe_config = config.copy()
            if safe_config.license_key:
                safe_config.license_key = "***" + safe_config.license_key[-4:] if len(safe_config.license_key) > 4 else "***"
            
            return SystemConfigResponse(
                success=True,
                message="系统配置更新成功",
                config=safe_config
            )
        else:
            raise HTTPException(status_code=500, detail="配置保存失败")
            
    except Exception as e:
        logger.error(f"更新系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新系统配置失败: {str(e)}")


@router.post("/license/verify", response_model=LicenseVerificationResponse)
async def verify_license(request: LicenseVerificationRequest):
    """验证授权密钥"""
    try:
        result = await auth_manager.verify_license(request.license_key)
        return result
        
    except Exception as e:
        logger.error(f"验证授权密钥失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证授权密钥失败: {str(e)}")


@router.post("/license/set", response_model=LicenseVerificationResponse)
async def set_license_key(request: LicenseVerificationRequest):
    """设置授权密钥"""
    try:
        result = await auth_manager.set_license_key(request.license_key)
        return result
        
    except Exception as e:
        logger.error(f"设置授权密钥失败: {e}")
        raise HTTPException(status_code=500, detail=f"设置授权密钥失败: {str(e)}")


@router.get("/license/status", response_model=Dict[str, Any])
async def get_license_status():
    """获取授权状态"""
    try:
        status = await auth_manager.get_license_status()
        return {
            "success": True,
            "message": "授权状态获取成功",
            "data": status
        }
        
    except Exception as e:
        logger.error(f"获取授权状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取授权状态失败: {str(e)}")


@router.get("/license/info", response_model=Dict[str, Any])
async def get_license_info():
    """获取详细授权信息"""
    try:
        info = await auth_manager.get_license_info()
        return {
            "success": True,
            "message": "授权信息获取成功",
            "data": info
        }
        
    except Exception as e:
        logger.error(f"获取授权信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取授权信息失败: {str(e)}")


@router.post("/auth/test-connection", response_model=Dict[str, Any])
async def test_auth_service_connection():
    """测试授权服务连接"""
    try:
        result = await auth_manager.test_auth_service_connection()
        return {
            "success": True,
            "message": "连接测试完成",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"测试授权服务连接失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试授权服务连接失败: {str(e)}")


@router.post("/auth/clear-cache", response_model=Dict[str, Any])
async def clear_auth_cache():
    """清除授权缓存"""
    try:
        auth_manager.clear_license_cache()
        return {
            "success": True,
            "message": "授权缓存已清除"
        }
        
    except Exception as e:
        logger.error(f"清除授权缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除授权缓存失败: {str(e)}")


@router.get("/stats", response_model=PlatformStatsResponse)
async def get_platform_stats():
    """获取平台统计信息"""
    try:
        # 获取算法统计
        from ..core.algorithm_manager import AlgorithmManager
        algorithm_manager = AlgorithmManager()
        algorithms = await algorithm_manager.list_algorithms()
        
        total_algorithms = len(algorithms)
        running_algorithms = len([a for a in algorithms if a["status"] == "running"])
        
        # 获取任务统计
        tasks = db_manager.get_tasks(limit=1000)  # 获取最近1000个任务
        total_tasks = len(tasks)
        successful_tasks = len([t for t in tasks if t.get("status") == "completed"])
        failed_tasks = len([t for t in tasks if t.get("status") == "failed"])
        
        # 获取系统资源信息
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        disk = psutil.disk_usage('/')
        
        # 计算系统运行时间
        uptime_seconds = time.time() - system_start_time
        uptime_hours = int(uptime_seconds // 3600)
        uptime_minutes = int((uptime_seconds % 3600) // 60)
        uptime_str = f"{uptime_hours}小时{uptime_minutes}分钟"
        
        stats = PlatformStats(
            total_algorithms=total_algorithms,
            running_algorithms=running_algorithms,
            total_tasks=total_tasks,
            successful_tasks=successful_tasks,
            failed_tasks=failed_tasks,
            system_uptime=uptime_str,
            memory_usage=memory.percent,
            cpu_usage=cpu_percent,
            disk_usage=disk.percent
        )
        
        return PlatformStatsResponse(
            success=True,
            message="平台统计信息获取成功",
            stats=stats
        )
        
    except Exception as e:
        logger.error(f"获取平台统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取平台统计信息失败: {str(e)}")


@router.get("/health", response_model=Dict[str, Any])
async def system_health_check():
    """系统健康检查"""
    try:
        # 检查数据库连接
        db_healthy = True
        try:
            db_manager.get_system_config()
        except Exception:
            db_healthy = False
        
        # 检查授权服务连接
        auth_result = await auth_manager.test_auth_service_connection()
        auth_healthy = auth_result.get("status") == "connected"
        
        # 检查系统资源
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        resource_healthy = memory.percent < 90 and disk.percent < 90
        
        # 综合健康状态
        overall_healthy = db_healthy and resource_healthy
        
        return {
            "success": True,
            "message": "系统健康检查完成",
            "data": {
                "overall_status": "healthy" if overall_healthy else "unhealthy",
                "components": {
                    "database": "healthy" if db_healthy else "unhealthy",
                    "auth_service": "healthy" if auth_healthy else "unhealthy",
                    "resources": "healthy" if resource_healthy else "unhealthy"
                },
                "details": {
                    "memory_usage": f"{memory.percent:.1f}%",
                    "disk_usage": f"{disk.percent:.1f}%",
                    "auth_service_url": auth_manager.config.auth_service_url,
                    "uptime": f"{(time.time() - system_start_time) / 3600:.1f} hours"
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"系统健康检查失败: {str(e)}")


@router.get("/info", response_model=Dict[str, Any])
async def get_system_info():
    """获取系统信息"""
    try:
        import platform
        import sys
        
        return {
            "success": True,
            "message": "系统信息获取成功",
            "data": {
                "platform": {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor()
                },
                "python": {
                    "version": sys.version,
                    "executable": sys.executable
                },
                "application": {
                    "name": "算法管理平台",
                    "version": "1.0.0",
                    "start_time": datetime.fromtimestamp(system_start_time).isoformat(),
                    "uptime_seconds": time.time() - system_start_time
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


@router.post("/restart", response_model=Dict[str, Any])
async def restart_system():
    """重启系统（仅重启应用服务）"""
    try:
        # 这里可以实现应用重启逻辑
        # 注意：在容器环境中，通常通过外部机制重启
        
        return {
            "success": True,
            "message": "系统重启请求已接收，请稍后检查服务状态"
        }
        
    except Exception as e:
        logger.error(f"重启系统失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启系统失败: {str(e)}")
