"""
API代理路由
负责将请求转发到对应的算法容器
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any
import logging

from ..core.api_gateway import APIGateway
from ..models.algorithm import APIProxyResponse, TaskListResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/algorithms", tags=["API代理"])

# 全局API网关实例
api_gateway = APIGateway()


@router.post("/{algorithm_name}/detect", response_model=APIProxyResponse)
async def proxy_detect(
    algorithm_name: str,
    request: Request,
    file: UploadFile = File(..., description="待检测的图像文件"),
    extract_features: Optional[bool] = Form(False, description="是否提取特征"),
    confidence_threshold: Optional[float] = Form(None, description="置信度阈值")
):
    """代理人脸/目标检测请求"""
    try:
        # 准备请求参数
        files = {"file": file.file}
        data = {}
        
        if extract_features is not None:
            data["extract_features"] = extract_features
        if confidence_threshold is not None:
            data["confidence_threshold"] = confidence_threshold
        
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 代理请求
        result = await api_gateway.proxy_request(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/detect",
            method="POST",
            files=files,
            data=data
        )
        
        # 记录额外的日志信息
        await api_gateway._log_api_call(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/detect",
            method="POST",
            status_code=result.status_code,
            processing_time=result.processing_time or 0,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return result
        
    except Exception as e:
        logger.error(f"代理检测请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理检测请求失败: {str(e)}")


@router.post("/{algorithm_name}/compare", response_model=APIProxyResponse)
async def proxy_compare(
    algorithm_name: str,
    request: Request,
    file1: UploadFile = File(..., description="第一张图像"),
    file2: UploadFile = File(..., description="第二张图像"),
    threshold: Optional[float] = Form(None, description="相似度阈值")
):
    """代理人脸比对请求"""
    try:
        # 准备请求参数
        files = {
            "file1": file1.file,
            "file2": file2.file
        }
        data = {}
        
        if threshold is not None:
            data["threshold"] = threshold
        
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 代理请求
        result = await api_gateway.proxy_request(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/compare",
            method="POST",
            files=files,
            data=data
        )
        
        # 记录日志
        await api_gateway._log_api_call(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/compare",
            method="POST",
            status_code=result.status_code,
            processing_time=result.processing_time or 0,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return result
        
    except Exception as e:
        logger.error(f"代理比对请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理比对请求失败: {str(e)}")


@router.post("/{algorithm_name}/quality", response_model=APIProxyResponse)
async def proxy_quality(
    algorithm_name: str,
    request: Request,
    file: UploadFile = File(..., description="待评估的图像文件")
):
    """代理质量评估请求"""
    try:
        # 准备请求参数
        files = {"file": file.file}
        
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 代理请求
        result = await api_gateway.proxy_request(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/quality",
            method="POST",
            files=files
        )
        
        # 记录日志
        await api_gateway._log_api_call(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/quality",
            method="POST",
            status_code=result.status_code,
            processing_time=result.processing_time or 0,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return result
        
    except Exception as e:
        logger.error(f"代理质量评估请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理质量评估请求失败: {str(e)}")


@router.post("/{algorithm_name}/classify", response_model=APIProxyResponse)
async def proxy_classify(
    algorithm_name: str,
    request: Request,
    file: UploadFile = File(..., description="待分类的图像文件"),
    top_k: Optional[int] = Form(5, description="返回前K个结果")
):
    """代理分类请求"""
    try:
        # 准备请求参数
        files = {"file": file.file}
        data = {}
        
        if top_k is not None:
            data["top_k"] = top_k
        
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 代理请求
        result = await api_gateway.proxy_request(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/classify",
            method="POST",
            files=files,
            data=data
        )
        
        # 记录日志
        await api_gateway._log_api_call(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/classify",
            method="POST",
            status_code=result.status_code,
            processing_time=result.processing_time or 0,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return result
        
    except Exception as e:
        logger.error(f"代理分类请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理分类请求失败: {str(e)}")


@router.get("/{algorithm_name}/health", response_model=APIProxyResponse)
async def proxy_health(algorithm_name: str, request: Request):
    """代理健康检查请求"""
    try:
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 代理请求
        result = await api_gateway.proxy_request(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/health",
            method="GET"
        )
        
        # 记录日志
        await api_gateway._log_api_call(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/health",
            method="GET",
            status_code=result.status_code,
            processing_time=result.processing_time or 0,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return result
        
    except Exception as e:
        logger.error(f"代理健康检查请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理健康检查请求失败: {str(e)}")


@router.get("/{algorithm_name}/info", response_model=APIProxyResponse)
async def proxy_info(algorithm_name: str, request: Request):
    """代理算法信息请求"""
    try:
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 代理请求
        result = await api_gateway.proxy_request(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/info",
            method="GET"
        )
        
        # 记录日志
        await api_gateway._log_api_call(
            algorithm_name=algorithm_name,
            endpoint="/api/v1/info",
            method="GET",
            status_code=result.status_code,
            processing_time=result.processing_time or 0,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return result
        
    except Exception as e:
        logger.error(f"代理算法信息请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理算法信息请求失败: {str(e)}")


# 网关管理接口
@router.get("/gateway/routing", response_model=Dict[str, Any])
async def get_routing_table():
    """获取API网关路由表"""
    try:
        result = await api_gateway.get_routing_table()
        return result
        
    except Exception as e:
        logger.error(f"获取路由表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取路由表失败: {str(e)}")


@router.get("/gateway/health", response_model=Dict[str, Any])
async def gateway_health_check():
    """API网关健康检查"""
    try:
        result = await api_gateway.health_check_algorithms()
        return result
        
    except Exception as e:
        logger.error(f"网关健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"网关健康检查失败: {str(e)}")


@router.get("/gateway/tasks/active", response_model=Dict[str, Any])
async def get_active_tasks():
    """获取活跃任务列表"""
    try:
        tasks = await api_gateway.get_active_tasks()
        return {
            "success": True,
            "message": "活跃任务获取成功",
            "tasks": tasks,
            "total": len(tasks)
        }
        
    except Exception as e:
        logger.error(f"获取活跃任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取活跃任务失败: {str(e)}")


@router.get("/gateway/tasks/history", response_model=Dict[str, Any])
async def get_task_history(limit: int = 100, offset: int = 0):
    """获取任务历史"""
    try:
        result = await api_gateway.get_task_history(limit=limit, offset=offset)
        return result
        
    except Exception as e:
        logger.error(f"获取任务历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务历史失败: {str(e)}")


@router.post("/gateway/tasks/{task_id}/cancel", response_model=Dict[str, Any])
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        result = await api_gateway.cancel_task(task_id)
        return result
        
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")
