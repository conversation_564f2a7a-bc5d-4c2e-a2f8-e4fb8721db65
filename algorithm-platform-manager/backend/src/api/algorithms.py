"""
算法管理API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from typing import Optional, List
import logging

from ..core.algorithm_manager import AlgorithmManager
from ..models.algorithm import (
    AlgorithmResponse, AlgorithmListResponse, AlgorithmStatusResponse,
    HealthCheckResponse, AlgorithmLogsResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/algorithms", tags=["算法管理"])

# 全局算法管理器实例
algorithm_manager = AlgorithmManager()


@router.get("/", response_model=AlgorithmListResponse)
async def list_algorithms(auto_scan: bool = True):
    """获取所有算法列表"""
    try:
        algorithms = await algorithm_manager.list_algorithms(auto_scan=auto_scan)

        return AlgorithmListResponse(
            success=True,
            message="算法列表获取成功",
            algorithms=algorithms,
            total=len(algorithms)
        )

    except Exception as e:
        logger.error(f"获取算法列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取算法列表失败: {str(e)}")


@router.post("/scan", response_model=AlgorithmResponse)
async def scan_docker_containers():
    """扫描Docker容器中的算法"""
    try:
        result = await algorithm_manager.scan_docker_containers()

        return AlgorithmResponse(
            success=True,
            message=result["message"],
            data=result
        )

    except Exception as e:
        logger.error(f"扫描Docker容器失败: {e}")
        raise HTTPException(status_code=500, detail=f"扫描Docker容器失败: {str(e)}")


@router.post("/refresh", response_model=AlgorithmListResponse)
async def refresh_algorithms():
    """刷新算法列表（重新扫描Docker容器）"""
    try:
        # 先扫描容器
        scan_result = await algorithm_manager.scan_docker_containers()

        # 再获取算法列表
        algorithms = await algorithm_manager.list_algorithms(auto_scan=False)

        return AlgorithmListResponse(
            success=True,
            message=f"刷新完成，{scan_result['message']}",
            algorithms=algorithms,
            total=len(algorithms)
        )

    except Exception as e:
        logger.error(f"刷新算法列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"刷新算法列表失败: {str(e)}")


@router.post("/upload", response_model=AlgorithmResponse)
async def upload_algorithm_package(
    file: UploadFile = File(..., description="算法包文件(.tar.gz格式)"),
    description: Optional[str] = Form(None, description="算法描述")
):
    """上传算法包"""
    try:
        # 验证文件格式
        if not file.filename.endswith(('.tar.gz', '.tgz')):
            raise HTTPException(status_code=400, detail="只支持 .tar.gz 格式的算法包")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 上传并解析算法包
        result = await algorithm_manager.upload_algorithm_package(file_content, file.filename)
        
        return AlgorithmResponse(
            success=True,
            message=result["message"],
            data=result
        )
        
    except Exception as e:
        logger.error(f"上传算法包失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传算法包失败: {str(e)}")


@router.post("/{algorithm_name}/build", response_model=AlgorithmResponse)
async def build_algorithm(algorithm_name: str):
    """构建算法Docker镜像"""
    try:
        result = await algorithm_manager.build_algorithm(algorithm_name)
        
        return AlgorithmResponse(
            success=True,
            message=result["message"],
            data=result
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"构建算法失败: {e}")
        raise HTTPException(status_code=500, detail=f"构建算法失败: {str(e)}")


@router.post("/{algorithm_name}/start", response_model=AlgorithmResponse)
async def start_algorithm(algorithm_name: str):
    """启动算法容器"""
    try:
        result = await algorithm_manager.start_algorithm(algorithm_name)
        
        return AlgorithmResponse(
            success=True,
            message=result["message"],
            data=result
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"启动算法失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动算法失败: {str(e)}")


@router.post("/{algorithm_name}/stop", response_model=AlgorithmResponse)
async def stop_algorithm(algorithm_name: str):
    """停止算法容器"""
    try:
        result = await algorithm_manager.stop_algorithm(algorithm_name)
        
        return AlgorithmResponse(
            success=True,
            message=result["message"],
            data=result
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"停止算法失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止算法失败: {str(e)}")


@router.post("/{algorithm_name}/restart", response_model=AlgorithmResponse)
async def restart_algorithm(algorithm_name: str):
    """重启算法容器"""
    try:
        result = await algorithm_manager.restart_algorithm(algorithm_name)
        
        return AlgorithmResponse(
            success=True,
            message=result["message"],
            data=result
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"重启算法失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启算法失败: {str(e)}")


@router.delete("/{algorithm_name}", response_model=AlgorithmResponse)
async def unload_algorithm(algorithm_name: str):
    """卸载算法（停止容器并删除镜像）"""
    try:
        result = await algorithm_manager.unload_algorithm(algorithm_name)
        
        return AlgorithmResponse(
            success=True,
            message=result["message"],
            data=result
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"卸载算法失败: {e}")
        raise HTTPException(status_code=500, detail=f"卸载算法失败: {str(e)}")


@router.get("/{algorithm_name}/status", response_model=AlgorithmStatusResponse)
async def get_algorithm_status(algorithm_name: str):
    """获取算法状态"""
    try:
        status = await algorithm_manager.get_algorithm_status(algorithm_name)
        
        return AlgorithmStatusResponse(
            success=True,
            message="算法状态获取成功",
            algorithm=status
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取算法状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取算法状态失败: {str(e)}")


@router.get("/{algorithm_name}/logs", response_model=AlgorithmLogsResponse)
async def get_algorithm_logs(
    algorithm_name: str,
    lines: int = 100
):
    """获取算法容器日志"""
    try:
        logs = await algorithm_manager.get_algorithm_logs(algorithm_name, lines)
        
        return AlgorithmLogsResponse(
            success=True,
            message="日志获取成功",
            logs=logs,
            algorithm_name=algorithm_name
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取算法日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取算法日志失败: {str(e)}")


@router.get("/health/check", response_model=HealthCheckResponse)
async def health_check_all():
    """对所有算法进行健康检查"""
    try:
        results = await algorithm_manager.health_check_all()
        
        return HealthCheckResponse(
            success=True,
            message="健康检查完成",
            results=results
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


# 便捷操作接口
@router.post("/{algorithm_name}/load", response_model=AlgorithmResponse)
async def load_algorithm(algorithm_name: str):
    """一键加载算法（构建+启动）"""
    try:
        # 先构建镜像
        build_result = await algorithm_manager.build_algorithm(algorithm_name)
        
        # 再启动容器
        start_result = await algorithm_manager.start_algorithm(algorithm_name)
        
        return AlgorithmResponse(
            success=True,
            message=f"算法 {algorithm_name} 加载成功",
            data={
                "build_result": build_result,
                "start_result": start_result
            }
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"加载算法失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载算法失败: {str(e)}")


@router.post("/{algorithm_name}/unload", response_model=AlgorithmResponse)
async def unload_algorithm_alias(algorithm_name: str):
    """卸载算法的别名接口"""
    return await unload_algorithm(algorithm_name)
