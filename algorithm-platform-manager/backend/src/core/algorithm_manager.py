"""
算法容器管理器
负责算法容器的生命周期管理、端口分配、健康检查等核心功能
"""

import docker
import asyncio
import aiohttp
import logging
import json
import tarfile
import yaml
import os
import re
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from ..models.algorithm import Algorithm, AlgorithmStatus, ContainerInfo
from .database import DatabaseManager

logger = logging.getLogger(__name__)


class AlgorithmManager:
    """算法容器管理器"""

    def __init__(self, data_dir: str = "/app/data"):
        self.docker_client = docker.from_env()
        self.algorithms: Dict[str, Algorithm] = {}
        self.port_counter = 8001
        self.data_dir = Path(data_dir)
        self.db = DatabaseManager()

        # 确保数据目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)
        (self.data_dir / "algorithms").mkdir(exist_ok=True)
        (self.data_dir / "uploads").mkdir(exist_ok=True)

        # 初始化时加载已有算法信息
        self._load_existing_algorithms()
    
    def _load_existing_algorithms(self):
        """加载已存在的算法信息"""
        try:
            algorithms = self.db.get_all_algorithms()
            for algo_data in algorithms:
                algorithm = Algorithm(**algo_data)
                self.algorithms[algorithm.name] = algorithm
                
                # 检查容器是否还在运行
                if algorithm.status == AlgorithmStatus.RUNNING:
                    if not self._is_container_running(algorithm.container_info.container_id):
                        algorithm.status = AlgorithmStatus.STOPPED
                        self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.STOPPED)
                        
            logger.info(f"加载了 {len(self.algorithms)} 个算法")
        except Exception as e:
            logger.error(f"加载算法信息失败: {e}")
    
    def _is_container_running(self, container_id: str) -> bool:
        """检查容器是否在运行"""
        try:
            container = self.docker_client.containers.get(container_id)
            return container.status == 'running'
        except docker.errors.NotFound:
            return False
        except Exception as e:
            logger.error(f"检查容器状态失败: {e}")
            return False
    
    def _get_next_port(self) -> int:
        """获取下一个可用端口"""
        while self.port_counter < 9000:
            port = self.port_counter
            self.port_counter += 1
            
            # 检查端口是否被占用
            if not self._is_port_in_use(port):
                return port
        
        raise RuntimeError("没有可用端口")
    
    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0
    
    async def upload_algorithm_package(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """上传并解析算法包"""
        try:
            # 保存上传的文件
            package_path = self.data_dir / "uploads" / filename
            with open(package_path, "wb") as f:
                f.write(file_content)
            
            # 解压算法包
            algorithm_name = filename.replace('.tar.gz', '').replace('.zip', '')
            extract_path = self.data_dir / "algorithms" / algorithm_name
            extract_path.mkdir(parents=True, exist_ok=True)
            
            if filename.endswith('.tar.gz'):
                with tarfile.open(package_path, 'r:gz') as tar:
                    tar.extractall(extract_path)
            else:
                raise ValueError("不支持的文件格式，请使用 .tar.gz 格式")
            
            # 读取算法元数据
            metadata_path = extract_path / "algorithm.yaml"
            if not metadata_path.exists():
                raise ValueError("算法包中缺少 algorithm.yaml 元数据文件")
            
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = yaml.safe_load(f)
            
            # 验证Dockerfile是否存在
            dockerfile_path = extract_path / "Dockerfile"
            if not dockerfile_path.exists():
                raise ValueError("算法包中缺少 Dockerfile")
            
            # 创建算法对象
            algorithm = Algorithm(
                name=algorithm_name,
                version=metadata.get('version', '1.0.0'),
                description=metadata.get('description', ''),
                docker_image=f"algo-{algorithm_name}:latest",
                api_port=metadata.get('api_port', 8001),
                status=AlgorithmStatus.UPLOADED,
                capabilities=metadata.get('capabilities', []),
                input_formats=metadata.get('input_formats', ['jpg', 'png']),
                package_path=str(extract_path),
                created_at=datetime.now()
            )
            
            # 保存到数据库和内存
            self.algorithms[algorithm_name] = algorithm
            self.db.save_algorithm(algorithm)
            
            logger.info(f"算法包 {algorithm_name} 上传成功")
            return {
                "status": "success",
                "algorithm": algorithm.dict(),
                "message": f"算法包 {algorithm_name} 上传成功"
            }
            
        except Exception as e:
            logger.error(f"上传算法包失败: {e}")
            raise RuntimeError(f"上传算法包失败: {str(e)}")
    
    async def build_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """构建算法Docker镜像"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")
        
        algorithm = self.algorithms[algorithm_name]
        
        try:
            logger.info(f"开始构建算法 {algorithm_name} 的Docker镜像")
            
            # 构建Docker镜像
            image, logs = self.docker_client.images.build(
                path=algorithm.package_path,
                tag=algorithm.docker_image,
                rm=True,
                forcerm=True
            )
            
            # 更新状态
            algorithm.status = AlgorithmStatus.BUILT
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.BUILT)
            
            logger.info(f"算法 {algorithm_name} 镜像构建成功")
            return {
                "status": "success",
                "image_id": image.id,
                "message": f"算法 {algorithm_name} 镜像构建成功"
            }
            
        except Exception as e:
            logger.error(f"构建算法镜像失败: {e}")
            algorithm.status = AlgorithmStatus.BUILD_FAILED
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.BUILD_FAILED)
            raise RuntimeError(f"构建算法镜像失败: {str(e)}")
    
    async def start_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """启动算法容器"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")
        
        algorithm = self.algorithms[algorithm_name]
        
        if algorithm.status == AlgorithmStatus.RUNNING:
            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 已在运行",
                "port": algorithm.container_info.host_port if algorithm.container_info else None
            }
        
        try:
            # 如果镜像未构建，先构建
            if algorithm.status != AlgorithmStatus.BUILT:
                await self.build_algorithm(algorithm_name)
            
            # 分配端口
            host_port = self._get_next_port()
            
            logger.info(f"启动算法容器 {algorithm_name}，端口: {host_port}")
            
            # 启动容器
            container = self.docker_client.containers.run(
                algorithm.docker_image,
                detach=True,
                ports={f'{algorithm.api_port}/tcp': host_port},
                name=f"algo-{algorithm_name}-{host_port}",
                network="bridge",
                restart_policy={"Name": "unless-stopped"}
            )
            
            # 更新算法信息
            algorithm.container_info = ContainerInfo(
                container_id=container.id,
                container_name=container.name,
                host_port=host_port,
                api_base_url=f"http://localhost:{host_port}",
                health_check_url=f"http://localhost:{host_port}/api/v1/health"
            )
            algorithm.status = AlgorithmStatus.STARTING
            
            # 保存到数据库
            self.db.update_algorithm_container_info(algorithm_name, algorithm.container_info)
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.STARTING)
            
            # 等待容器启动并进行健康检查
            await self._wait_for_container_ready(algorithm)
            
            logger.info(f"算法 {algorithm_name} 启动成功，端口: {host_port}")
            return {
                "status": "success",
                "port": host_port,
                "container_id": container.id,
                "api_url": algorithm.container_info.api_base_url,
                "message": f"算法 {algorithm_name} 启动成功"
            }
            
        except Exception as e:
            logger.error(f"启动算法容器失败: {e}")
            algorithm.status = AlgorithmStatus.START_FAILED
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.START_FAILED)
            raise RuntimeError(f"启动算法容器失败: {str(e)}")
    
    async def _wait_for_container_ready(self, algorithm: Algorithm, timeout: int = 60):
        """等待容器就绪"""
        if not algorithm.container_info:
            raise RuntimeError("容器信息不存在")
        
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < timeout:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        algorithm.container_info.health_check_url,
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        if response.status == 200:
                            algorithm.status = AlgorithmStatus.RUNNING
                            self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.RUNNING)
                            logger.info(f"算法 {algorithm.name} 健康检查通过")
                            return
            except Exception:
                pass
            
            await asyncio.sleep(2)
        
        # 超时，标记为启动失败
        algorithm.status = AlgorithmStatus.START_FAILED
        self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.START_FAILED)
        raise RuntimeError(f"算法 {algorithm.name} 启动超时")

    async def stop_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """停止算法容器"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        algorithm = self.algorithms[algorithm_name]

        if algorithm.status != AlgorithmStatus.RUNNING:
            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 未在运行"
            }

        try:
            if algorithm.container_info:
                container = self.docker_client.containers.get(algorithm.container_info.container_id)
                container.stop(timeout=10)
                container.remove()

                logger.info(f"算法 {algorithm_name} 容器已停止")

            # 更新状态
            algorithm.status = AlgorithmStatus.STOPPED
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.STOPPED)

            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 已停止"
            }

        except Exception as e:
            logger.error(f"停止算法容器失败: {e}")
            raise RuntimeError(f"停止算法容器失败: {str(e)}")

    async def restart_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """重启算法容器"""
        try:
            await self.stop_algorithm(algorithm_name)
            await asyncio.sleep(2)  # 等待容器完全停止
            return await self.start_algorithm(algorithm_name)
        except Exception as e:
            logger.error(f"重启算法容器失败: {e}")
            raise RuntimeError(f"重启算法容器失败: {str(e)}")

    async def unload_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """卸载算法（停止容器并删除镜像）"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        try:
            # 先停止容器
            await self.stop_algorithm(algorithm_name)

            algorithm = self.algorithms[algorithm_name]

            # 删除Docker镜像
            try:
                self.docker_client.images.remove(algorithm.docker_image, force=True)
                logger.info(f"算法 {algorithm_name} 镜像已删除")
            except docker.errors.ImageNotFound:
                pass

            # 从内存和数据库中删除
            del self.algorithms[algorithm_name]
            self.db.delete_algorithm(algorithm_name)

            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 已卸载"
            }

        except Exception as e:
            logger.error(f"卸载算法失败: {e}")
            raise RuntimeError(f"卸载算法失败: {str(e)}")

    async def get_algorithm_status(self, algorithm_name: str) -> Dict[str, Any]:
        """获取算法状态"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        algorithm = self.algorithms[algorithm_name]

        # 实时检查容器状态
        if algorithm.container_info and algorithm.status == AlgorithmStatus.RUNNING:
            is_running = self._is_container_running(algorithm.container_info.container_id)
            if not is_running:
                algorithm.status = AlgorithmStatus.STOPPED
                self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.STOPPED)

        return {
            "name": algorithm.name,
            "status": algorithm.status.value,
            "version": algorithm.version,
            "description": algorithm.description,
            "container_info": algorithm.container_info.dict() if algorithm.container_info else None,
            "capabilities": algorithm.capabilities,
            "created_at": algorithm.created_at.isoformat() if algorithm.created_at else None
        }

    async def list_algorithms(self, auto_scan: bool = True) -> List[Dict[str, Any]]:
        """获取所有算法列表"""
        # 如果启用自动扫描，先扫描Docker容器
        if auto_scan:
            await self.scan_docker_containers()

        algorithms = []
        for algorithm in self.algorithms.values():
            # 实时检查状态
            if algorithm.container_info and algorithm.status == AlgorithmStatus.RUNNING:
                is_running = self._is_container_running(algorithm.container_info.container_id)
                if not is_running:
                    algorithm.status = AlgorithmStatus.STOPPED
                    self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.STOPPED)

            algorithms.append({
                "name": algorithm.name,
                "status": algorithm.status.value,
                "version": algorithm.version,
                "description": algorithm.description,
                "docker_image": algorithm.docker_image,
                "api_url": algorithm.container_info.api_base_url if algorithm.container_info else None,
                "host_port": algorithm.container_info.host_port if algorithm.container_info else None,
                "container_id": algorithm.container_info.container_id if algorithm.container_info else None,
                "container_name": algorithm.container_info.container_name if algorithm.container_info else None,
                "capabilities": algorithm.capabilities,
                "created_at": algorithm.created_at.isoformat() if algorithm.created_at else None
            })

        return algorithms

    async def health_check_all(self) -> Dict[str, Any]:
        """对所有运行中的算法进行健康检查"""
        results = {}

        for algorithm in self.algorithms.values():
            if algorithm.status == AlgorithmStatus.RUNNING and algorithm.container_info:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            algorithm.container_info.health_check_url,
                            timeout=aiohttp.ClientTimeout(total=5)
                        ) as response:
                            results[algorithm.name] = {
                                "status": "healthy" if response.status == 200 else "unhealthy",
                                "response_time": response.headers.get("X-Response-Time", "unknown"),
                                "last_check": datetime.now().isoformat()
                            }
                except Exception as e:
                    results[algorithm.name] = {
                        "status": "unhealthy",
                        "error": str(e),
                        "last_check": datetime.now().isoformat()
                    }

                    # 更新状态为异常
                    algorithm.status = AlgorithmStatus.UNHEALTHY
                    self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.UNHEALTHY)

        return results

    def _is_algorithm_container(self, container) -> bool:
        """判断容器是否为算法容器 - 仅通过标签识别"""
        try:
            # 只检查容器标签 algorithm.platform="true"
            labels = container.labels or {}
            return labels.get('algorithm.platform') == 'true'

        except Exception as e:
            logger.error(f"检查容器类型失败: {e}")
            return False

    def _extract_container_info(self, container) -> Dict[str, Any]:
        """提取容器信息"""
        try:
            labels = container.labels or {}

            # 提取端口信息
            host_port = None
            api_port = None
            if hasattr(container, 'ports') and container.ports:
                for internal_port, port_mappings in container.ports.items():
                    if port_mappings:
                        host_port = int(port_mappings[0]['HostPort'])
                        api_port = int(internal_port.split('/')[0])
                        break

            # 构建API URL
            api_base_url = f"http://localhost:{host_port}" if host_port else None
            health_check_url = f"{api_base_url}/api/v1/health" if api_base_url else None

            return {
                'container_id': container.id,
                'container_name': container.name,
                'image_name': container.image.tags[0] if container.image.tags else container.image.id,
                'status': container.status,
                'created_at': container.attrs.get('Created', ''),
                'host_port': host_port,
                'api_port': api_port,
                'api_base_url': api_base_url,
                'health_check_url': health_check_url,
                'labels': labels,
                'algorithm_name': labels.get('algorithm.name', container.name),
                'algorithm_type': labels.get('algorithm.type', 'unknown'),
                'algorithm_version': labels.get('algorithm.version', '1.0.0'),
                'algorithm_description': labels.get('algorithm.description', ''),
            }

        except Exception as e:
            logger.error(f"提取容器信息失败: {e}")
            return None

    async def scan_docker_containers(self) -> Dict[str, Any]:
        """扫描Docker中的算法容器"""
        try:
            logger.info("开始扫描Docker容器中的算法...")

            # 获取所有运行中的容器
            containers = self.docker_client.containers.list(all=True)

            algorithm_containers = []
            scanned_count = 0
            algorithm_count = 0

            for container in containers:
                scanned_count += 1

                # 检查是否为算法容器
                if self._is_algorithm_container(container):
                    algorithm_count += 1

                    # 提取容器信息
                    container_info = self._extract_container_info(container)
                    if container_info:
                        # 检查健康状态
                        if container.status == 'running' and container_info['health_check_url']:
                            health_status = await self._check_container_health(container_info['health_check_url'])
                            container_info['health_status'] = health_status
                        else:
                            container_info['health_status'] = 'stopped'

                        algorithm_containers.append(container_info)

                        # 同步到内存中的算法列表
                        await self._sync_discovered_algorithm(container_info)

            logger.info(f"扫描完成: 总容器 {scanned_count} 个, 算法容器 {algorithm_count} 个")

            return {
                'status': 'success',
                'scanned_containers': scanned_count,
                'algorithm_containers': algorithm_count,
                'algorithms': algorithm_containers,
                'message': f'发现 {algorithm_count} 个算法容器'
            }

        except Exception as e:
            logger.error(f"扫描Docker容器失败: {e}")
            return {
                'status': 'error',
                'message': f'扫描失败: {str(e)}',
                'algorithms': []
            }

    async def _check_container_health(self, health_url: str) -> str:
        """检查容器健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    health_url,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        return 'healthy'
                    else:
                        return 'unhealthy'
        except Exception:
            return 'unreachable'

    async def _sync_discovered_algorithm(self, container_info: Dict[str, Any]):
        """同步发现的算法到内存列表"""
        try:
            algorithm_name = container_info['algorithm_name']

            # 检查是否已存在
            if algorithm_name in self.algorithms:
                # 更新现有算法的容器信息
                algorithm = self.algorithms[algorithm_name]
                algorithm.container_info = ContainerInfo(
                    container_id=container_info['container_id'],
                    container_name=container_info['container_name'],
                    host_port=container_info['host_port'],
                    api_base_url=container_info['api_base_url'],
                    health_check_url=container_info['health_check_url']
                )

                # 更新状态
                if container_info['status'] == 'running':
                    if container_info['health_status'] == 'healthy':
                        algorithm.status = AlgorithmStatus.RUNNING
                    else:
                        algorithm.status = AlgorithmStatus.UNHEALTHY
                else:
                    algorithm.status = AlgorithmStatus.STOPPED

            else:
                # 创建新的算法对象
                algorithm = Algorithm(
                    name=algorithm_name,
                    version=container_info['algorithm_version'],
                    description=container_info['algorithm_description'],
                    docker_image=container_info['image_name'],
                    api_port=container_info['api_port'] or 8001,
                    status=AlgorithmStatus.RUNNING if container_info['status'] == 'running' else AlgorithmStatus.STOPPED,
                    capabilities=[],
                    input_formats=['jpg', 'png'],
                    created_at=datetime.now(),
                    container_info=ContainerInfo(
                        container_id=container_info['container_id'],
                        container_name=container_info['container_name'],
                        host_port=container_info['host_port'],
                        api_base_url=container_info['api_base_url'],
                        health_check_url=container_info['health_check_url']
                    )
                )

                self.algorithms[algorithm_name] = algorithm

                # 保存到数据库
                self.db.save_algorithm(algorithm)

        except Exception as e:
            logger.error(f"同步算法信息失败: {e}")

    async def get_algorithm_logs(self, algorithm_name: str, lines: int = 100) -> List[str]:
        """获取算法容器日志"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        algorithm = self.algorithms[algorithm_name]

        if not algorithm.container_info:
            return []

        try:
            container = self.docker_client.containers.get(algorithm.container_info.container_id)
            logs = container.logs(tail=lines, timestamps=True).decode('utf-8')
            return logs.split('\n')
        except Exception as e:
            logger.error(f"获取容器日志失败: {e}")
            return [f"获取日志失败: {str(e)}"]
