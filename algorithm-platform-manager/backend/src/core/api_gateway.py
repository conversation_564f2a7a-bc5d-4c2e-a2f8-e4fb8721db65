"""
API网关模块
负责请求路由、代理转发、负载均衡等功能
"""

import aiohttp
import asyncio
import logging
import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

from .algorithm_manager import AlgorithmManager
from .auth_manager import AuthManager
from .database import DatabaseManager
from ..models.algorithm import APIProxyResponse, TaskInfo

logger = logging.getLogger(__name__)


class APIGateway:
    """API网关"""
    
    def __init__(self):
        self.algorithm_manager = AlgorithmManager()
        self.auth_manager = AuthManager()
        self.db = DatabaseManager()
        self.active_tasks: Dict[str, TaskInfo] = {}
    
    async def proxy_request(
        self,
        algorithm_name: str,
        endpoint: str,
        method: str = "POST",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None,
        files: Optional[Dict[str, Any]] = None,
        timeout: int = 30
    ) -> APIProxyResponse:
        """代理API请求到算法容器"""
        
        start_time = time.time()
        task_id = str(uuid.uuid4())
        
        try:
            # 检查算法是否存在
            if algorithm_name not in self.algorithm_manager.algorithms:
                return APIProxyResponse(
                    success=False,
                    message=f"算法 {algorithm_name} 不存在",
                    status_code=404,
                    processing_time=time.time() - start_time
                )
            
            algorithm = self.algorithm_manager.algorithms[algorithm_name]
            
            # 检查算法是否在运行
            if not algorithm.container_info or algorithm.status.value != "running":
                return APIProxyResponse(
                    success=False,
                    message=f"算法 {algorithm_name} 未运行",
                    status_code=503,
                    processing_time=time.time() - start_time
                )
            
            # 检查授权权限
            has_permission = await self.auth_manager.check_api_permission(algorithm_name, endpoint)
            if not has_permission:
                return APIProxyResponse(
                    success=False,
                    message="授权验证失败",
                    status_code=401,
                    processing_time=time.time() - start_time
                )
            
            # 构建目标URL
            target_url = f"{algorithm.container_info.api_base_url}{endpoint}"
            
            # 创建任务记录
            task = TaskInfo(
                task_id=task_id,
                algorithm_name=algorithm_name,
                endpoint=endpoint,
                status="processing",
                input_data={
                    "method": method,
                    "params": params,
                    "headers": headers
                },
                created_at=datetime.now()
            )
            self.active_tasks[task_id] = task
            
            # 发送请求
            async with aiohttp.ClientSession() as session:
                request_kwargs = {
                    "url": target_url,
                    "method": method,
                    "timeout": aiohttp.ClientTimeout(total=timeout)
                }
                
                if headers:
                    request_kwargs["headers"] = headers
                if params:
                    request_kwargs["params"] = params
                if data:
                    request_kwargs["json"] = data
                if files:
                    # 处理文件上传
                    form_data = aiohttp.FormData()
                    for key, value in files.items():
                        if hasattr(value, 'read'):
                            # 文件对象
                            form_data.add_field(key, value)
                        else:
                            # 其他数据
                            form_data.add_field(key, value)
                    request_kwargs["data"] = form_data
                
                async with session.request(**request_kwargs) as response:
                    response_data = None
                    response_headers = dict(response.headers)
                    
                    try:
                        # 尝试解析JSON响应
                        response_data = await response.json()
                    except:
                        # 如果不是JSON，获取文本内容
                        response_text = await response.text()
                        response_data = {"content": response_text}
                    
                    processing_time = time.time() - start_time
                    
                    # 更新任务状态
                    task.status = "completed" if response.status < 400 else "failed"
                    task.output_data = response_data
                    task.processing_time = processing_time
                    task.completed_at = datetime.now()
                    
                    if response.status >= 400:
                        task.error_message = f"HTTP {response.status}: {response.reason}"
                    
                    # 保存任务到数据库
                    self.db.save_task(task.dict())
                    
                    # 记录API调用日志
                    await self._log_api_call(
                        algorithm_name=algorithm_name,
                        endpoint=endpoint,
                        method=method,
                        status_code=response.status,
                        processing_time=processing_time,
                        request_size=len(str(data)) if data else 0,
                        response_size=len(str(response_data)) if response_data else 0
                    )
                    
                    # 从活跃任务中移除
                    if task_id in self.active_tasks:
                        del self.active_tasks[task_id]
                    
                    return APIProxyResponse(
                        success=response.status < 400,
                        message="请求处理成功" if response.status < 400 else f"请求失败: {response.reason}",
                        status_code=response.status,
                        data=response_data,
                        headers=response_headers,
                        processing_time=processing_time
                    )
                    
        except aiohttp.ClientError as e:
            processing_time = time.time() - start_time
            error_message = f"网络请求失败: {str(e)}"
            
            # 更新任务状态
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                task.status = "failed"
                task.error_message = error_message
                task.processing_time = processing_time
                task.completed_at = datetime.now()
                self.db.save_task(task.dict())
                del self.active_tasks[task_id]
            
            logger.error(f"代理请求失败: {e}")
            return APIProxyResponse(
                success=False,
                message=error_message,
                status_code=502,
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_message = f"内部错误: {str(e)}"
            
            # 更新任务状态
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                task.status = "failed"
                task.error_message = error_message
                task.processing_time = processing_time
                task.completed_at = datetime.now()
                self.db.save_task(task.dict())
                del self.active_tasks[task_id]
            
            logger.error(f"代理请求异常: {e}")
            return APIProxyResponse(
                success=False,
                message=error_message,
                status_code=500,
                processing_time=processing_time
            )
    
    async def _log_api_call(
        self,
        algorithm_name: str,
        endpoint: str,
        method: str,
        status_code: int,
        processing_time: float,
        request_size: int = 0,
        response_size: int = 0,
        client_ip: str = "unknown",
        user_agent: str = "unknown"
    ):
        """记录API调用日志"""
        try:
            log_data = {
                "algorithm_name": algorithm_name,
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "processing_time": processing_time,
                "request_size": request_size,
                "response_size": response_size,
                "client_ip": client_ip,
                "user_agent": user_agent
            }
            
            self.db.log_api_call(log_data)
            
        except Exception as e:
            logger.error(f"记录API日志失败: {e}")
    
    async def get_routing_table(self) -> Dict[str, Any]:
        """获取路由表"""
        try:
            algorithms = await self.algorithm_manager.list_algorithms()
            
            routing_table = {}
            for algo in algorithms:
                if algo["status"] == "running" and algo.get("api_url"):
                    routing_table[algo["name"]] = {
                        "api_url": algo["api_url"],
                        "status": algo["status"],
                        "capabilities": algo.get("capabilities", []),
                        "endpoints": [
                            "/api/v1/health",
                            "/api/v1/info",
                            "/api/v1/detect",
                            "/api/v1/compare",
                            "/api/v1/quality"
                        ]
                    }
            
            return {
                "success": True,
                "message": "路由表获取成功",
                "routing_table": routing_table,
                "total_routes": len(routing_table)
            }
            
        except Exception as e:
            logger.error(f"获取路由表失败: {e}")
            return {
                "success": False,
                "message": f"获取路由表失败: {str(e)}",
                "routing_table": {},
                "total_routes": 0
            }
    
    async def health_check_algorithms(self) -> Dict[str, Any]:
        """对所有算法进行健康检查"""
        try:
            results = await self.algorithm_manager.health_check_all()
            
            healthy_count = sum(1 for r in results.values() if r.get("status") == "healthy")
            total_count = len(results)
            
            return {
                "success": True,
                "message": "健康检查完成",
                "results": results,
                "summary": {
                    "total": total_count,
                    "healthy": healthy_count,
                    "unhealthy": total_count - healthy_count,
                    "health_rate": f"{(healthy_count/total_count*100):.1f}%" if total_count > 0 else "0%"
                }
            }
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "success": False,
                "message": f"健康检查失败: {str(e)}",
                "results": {},
                "summary": {"total": 0, "healthy": 0, "unhealthy": 0, "health_rate": "0%"}
            }
    
    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃任务列表"""
        return [task.dict() for task in self.active_tasks.values()]
    
    async def get_task_history(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """获取任务历史"""
        try:
            tasks = self.db.get_tasks(limit=limit, offset=offset)
            
            return {
                "success": True,
                "message": "任务历史获取成功",
                "tasks": tasks,
                "total": len(tasks)
            }
            
        except Exception as e:
            logger.error(f"获取任务历史失败: {e}")
            return {
                "success": False,
                "message": f"获取任务历史失败: {str(e)}",
                "tasks": [],
                "total": 0
            }
    
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消活跃任务"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            task.status = "cancelled"
            task.completed_at = datetime.now()
            task.error_message = "任务被用户取消"
            
            # 保存到数据库
            self.db.save_task(task.dict())
            
            # 从活跃任务中移除
            del self.active_tasks[task_id]
            
            return {
                "success": True,
                "message": f"任务 {task_id} 已取消"
            }
        else:
            return {
                "success": False,
                "message": f"任务 {task_id} 不存在或已完成"
            }
