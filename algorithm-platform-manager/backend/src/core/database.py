"""
数据库管理模块
使用SQLite作为轻量级数据库，存储算法元数据、配置信息等
"""

import sqlite3
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from ..models.algorithm import Algorithm, AlgorithmStatus, ContainerInfo, SystemConfig

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "/app/data/platform.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建算法表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS algorithms (
                        name TEXT PRIMARY KEY,
                        version TEXT NOT NULL,
                        description TEXT,
                        docker_image TEXT NOT NULL,
                        api_port INTEGER DEFAULT 8001,
                        status TEXT NOT NULL,
                        capabilities TEXT,  -- JSON array
                        input_formats TEXT, -- JSON array
                        package_path TEXT,
                        container_info TEXT, -- JSON object
                        created_at TEXT,
                        updated_at TEXT
                    )
                ''')
                
                # 创建系统配置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_config (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_at TEXT
                    )
                ''')
                
                # 创建任务表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS tasks (
                        task_id TEXT PRIMARY KEY,
                        algorithm_name TEXT NOT NULL,
                        endpoint TEXT NOT NULL,
                        status TEXT NOT NULL,
                        input_data TEXT,  -- JSON object
                        output_data TEXT, -- JSON object
                        error_message TEXT,
                        processing_time REAL,
                        created_at TEXT,
                        completed_at TEXT,
                        FOREIGN KEY (algorithm_name) REFERENCES algorithms (name)
                    )
                ''')
                
                # 创建日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        algorithm_name TEXT,
                        endpoint TEXT,
                        method TEXT,
                        status_code INTEGER,
                        processing_time REAL,
                        request_size INTEGER,
                        response_size INTEGER,
                        client_ip TEXT,
                        user_agent TEXT,
                        created_at TEXT
                    )
                ''')
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_algorithm(self, algorithm: Algorithm) -> bool:
        """保存算法信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO algorithms (
                        name, version, description, docker_image, api_port, status,
                        capabilities, input_formats, package_path, container_info,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    algorithm.name,
                    algorithm.version,
                    algorithm.description,
                    algorithm.docker_image,
                    algorithm.api_port,
                    algorithm.status.value,
                    json.dumps(algorithm.capabilities),
                    json.dumps(algorithm.input_formats),
                    algorithm.package_path,
                    json.dumps(algorithm.container_info.dict()) if algorithm.container_info else None,
                    algorithm.created_at.isoformat() if algorithm.created_at else None,
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                logger.info(f"算法 {algorithm.name} 保存成功")
                return True
                
        except Exception as e:
            logger.error(f"保存算法失败: {e}")
            return False
    
    def get_algorithm(self, name: str) -> Optional[Dict[str, Any]]:
        """获取单个算法信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM algorithms WHERE name = ?', (name,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_algorithm_dict(row)
                return None
                
        except Exception as e:
            logger.error(f"获取算法信息失败: {e}")
            return None
    
    def get_all_algorithms(self) -> List[Dict[str, Any]]:
        """获取所有算法信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM algorithms ORDER BY created_at DESC')
                rows = cursor.fetchall()
                
                return [self._row_to_algorithm_dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取算法列表失败: {e}")
            return []
    
    def _row_to_algorithm_dict(self, row) -> Dict[str, Any]:
        """将数据库行转换为算法字典"""
        columns = [
            'name', 'version', 'description', 'docker_image', 'api_port', 'status',
            'capabilities', 'input_formats', 'package_path', 'container_info',
            'created_at', 'updated_at'
        ]
        
        data = dict(zip(columns, row))
        
        # 解析JSON字段
        if data['capabilities']:
            data['capabilities'] = json.loads(data['capabilities'])
        else:
            data['capabilities'] = []
            
        if data['input_formats']:
            data['input_formats'] = json.loads(data['input_formats'])
        else:
            data['input_formats'] = ['jpg', 'png']
            
        if data['container_info']:
            data['container_info'] = ContainerInfo(**json.loads(data['container_info']))
        else:
            data['container_info'] = None
            
        # 转换时间字段
        if data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data['updated_at']:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
            
        return data
    
    def update_algorithm_status(self, name: str, status: AlgorithmStatus) -> bool:
        """更新算法状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'UPDATE algorithms SET status = ?, updated_at = ? WHERE name = ?',
                    (status.value, datetime.now().isoformat(), name)
                )
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"更新算法状态失败: {e}")
            return False
    
    def update_algorithm_container_info(self, name: str, container_info: ContainerInfo) -> bool:
        """更新算法容器信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'UPDATE algorithms SET container_info = ?, updated_at = ? WHERE name = ?',
                    (json.dumps(container_info.dict()), datetime.now().isoformat(), name)
                )
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"更新容器信息失败: {e}")
            return False
    
    def delete_algorithm(self, name: str) -> bool:
        """删除算法"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM algorithms WHERE name = ?', (name,))
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"删除算法失败: {e}")
            return False
    
    def save_system_config(self, config: SystemConfig) -> bool:
        """保存系统配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                config_dict = config.dict()
                for key, value in config_dict.items():
                    cursor.execute(
                        'INSERT OR REPLACE INTO system_config (key, value, updated_at) VALUES (?, ?, ?)',
                        (key, json.dumps(value), datetime.now().isoformat())
                    )
                
                conn.commit()
                logger.info("系统配置保存成功")
                return True
                
        except Exception as e:
            logger.error(f"保存系统配置失败: {e}")
            return False
    
    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT key, value FROM system_config')
                rows = cursor.fetchall()
                
                config_dict = {}
                for key, value in rows:
                    config_dict[key] = json.loads(value)
                
                return SystemConfig(**config_dict)
                
        except Exception as e:
            logger.error(f"获取系统配置失败: {e}")
            return SystemConfig()  # 返回默认配置
    
    def save_task(self, task_data: Dict[str, Any]) -> bool:
        """保存任务信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO tasks (
                        task_id, algorithm_name, endpoint, status, input_data,
                        output_data, error_message, processing_time, created_at, completed_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task_data['task_id'],
                    task_data['algorithm_name'],
                    task_data['endpoint'],
                    task_data['status'],
                    json.dumps(task_data.get('input_data')),
                    json.dumps(task_data.get('output_data')),
                    task_data.get('error_message'),
                    task_data.get('processing_time'),
                    task_data['created_at'],
                    task_data.get('completed_at')
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存任务失败: {e}")
            return False
    
    def get_tasks(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取任务列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT * FROM tasks ORDER BY created_at DESC LIMIT ? OFFSET ?',
                    (limit, offset)
                )
                rows = cursor.fetchall()
                
                columns = [
                    'task_id', 'algorithm_name', 'endpoint', 'status', 'input_data',
                    'output_data', 'error_message', 'processing_time', 'created_at', 'completed_at'
                ]
                
                tasks = []
                for row in rows:
                    task = dict(zip(columns, row))
                    
                    # 解析JSON字段
                    if task['input_data']:
                        task['input_data'] = json.loads(task['input_data'])
                    if task['output_data']:
                        task['output_data'] = json.loads(task['output_data'])
                    
                    tasks.append(task)
                
                return tasks
                
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return []
    
    def log_api_call(self, log_data: Dict[str, Any]) -> bool:
        """记录API调用日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO api_logs (
                        algorithm_name, endpoint, method, status_code, processing_time,
                        request_size, response_size, client_ip, user_agent, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    log_data.get('algorithm_name'),
                    log_data.get('endpoint'),
                    log_data.get('method'),
                    log_data.get('status_code'),
                    log_data.get('processing_time'),
                    log_data.get('request_size'),
                    log_data.get('response_size'),
                    log_data.get('client_ip'),
                    log_data.get('user_agent'),
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"记录API日志失败: {e}")
            return False
