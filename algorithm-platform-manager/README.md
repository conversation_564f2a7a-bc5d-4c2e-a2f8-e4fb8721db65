# 算法管理平台

## 项目概述

统一管理和调用AI算法容器的专业平台，使用uv虚拟环境管理。

## 🚀 核心特性

- **uv虚拟环境**: 使用现代Python包管理器uv
- **Vue 3 + Vite**: 现代前端技术栈
- **FastAPI后端**: 高性能异步API
- **Docker容器化**: 完整的容器化部署
- **模块化设计**: 清晰的代码结构，易于维护
- **系统监控**: 实时状态和日志监控

### 🔧 核心功能
- **容器管理**: 动态启动/停止/重启算法容器
- **API网关**: 统一的API路由和代理转发
- **服务发现**: 自动发现和注册算法服务
- **健康检查**: 实时监控容器和服务状态

### 🎨 管理界面
- **算法管理**: 可视化的算法加载/卸载操作
- **系统配置**: 授权密钥配置和验证
- **在线测试**: API接口在线测试工具
- **状态监控**: 实时查看服务状态和日志

### 🚀 部署特性
- **一体化容器**: 前后端打包在单个Docker容器中
- **Docker-in-Docker**: 支持管理其他算法容器
- **一键启动**: 简单的部署和使用流程
- **数据持久化**: 配置和数据自动保存

## 快速开始

### 开发环境启动

```bash
# 1. 启动开发环境
docker-compose up -d

# 2. 访问服务
# 前端界面: http://localhost:3000
# 后端API: http://localhost:8100
# API文档: http://localhost:8100/docs
```

### 生产环境部署

```bash
# 1. 构建并启动平台
docker build -t algorithm-platform:latest .
docker run -d \
  --name algorithm-platform \
  -p 3000:3000 \
  -p 8100:8100 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v algorithm-data:/app/data \
  algorithm-platform:latest

# 2. 访问Web界面
open http://localhost:3000
```

### 关闭和清理

```bash
# 方式1: 停止容器
docker stop algorithm-platform-test algorithm-platform-frontend

# 方式2: 停止并删除容器（推荐）
docker stop algorithm-platform-test algorithm-platform-frontend
docker rm algorithm-platform-test algorithm-platform-frontend

# 方式3: 一键停止所有相关容器
docker stop $(docker ps -q --filter "name=algorithm-platform")
docker rm $(docker ps -aq --filter "name=algorithm-platform")

# 方式4: 如果使用docker-compose
docker-compose down

# 完全清理（删除镜像和数据）
docker-compose down -v
docker rmi algorithm-platform-backend:latest
docker system prune -f
```

## API接口

### 平台管理API

```bash
# 获取算法列表
GET /api/algorithms

# 加载算法
POST /api/algorithms/{name}/load

# 卸载算法
POST /api/algorithms/{name}/unload

# 获取算法状态
GET /api/algorithms/{name}/status

# 上传算法包
POST /api/algorithms/upload
```

### 算法调用API (代理转发)

```bash
# 人脸检测
POST /api/algorithms/wenzhou_face/detect

# 人车非检测
POST /api/algorithms/renchefei/detect

# 事故分类
POST /api/algorithms/accident/classify
```

## 技术架构

### 后端技术栈
- **FastAPI**: 高性能API框架
- **Docker SDK**: 容器管理
- **SQLite**: 轻量级数据库
- **Pydantic**: 数据验证
- **Uvicorn**: ASGI服务器

### 前端技术栈
- **Vue 3**: 现代前端框架
- **Element Plus**: UI组件库
- **Axios**: HTTP客户端
- **Vite**: 构建工具

### 部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                    平台管理容器                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │   前端界面   │ │  后端API    │ │  容器管理    │            │
│  │  (Vue.js)   │ │ (FastAPI)   │ │ (Docker)    │            │
│  │   :3000     │ │   :8100     │ │             │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
                           │
                           │ 动态管理
                           ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ 算法容器A    │ │ 算法容器B    │ │ 算法容器C    │
│ wenzhou_face│ │ renchefei   │ │ accident    │
│ :8001       │ │ :8002       │ │ :8003       │
└─────────────┘ └─────────────┘ └─────────────┘
```

## 开发指南

### 项目结构
```
algorithm-platform-manager/
├── backend/                    # 后端服务
│   ├── src/
│   │   ├── core/              # 核心模块
│   │   ├── api/               # API路由
│   │   ├── models/            # 数据模型
│   │   └── main.py            # 主应用
│   └── requirements.txt
├── frontend/                   # 前端界面
├── docker-compose.yml         # 开发环境
└── Dockerfile                 # 生产环境
```

### 环境要求
- Python 3.11+
- Node.js 18+
- Docker 20.10+
- 内存: 4GB+
- 磁盘: 20GB+

## 容器管理

### 启动服务
```bash
# 使用构建脚本启动开发环境
./build.sh development
./start.sh development

# 使用构建脚本启动生产环境
./build.sh production
./start.sh production

# 手动启动容器
docker run -d --name algorithm-platform-backend -p 8100:8100 algorithm-platform-backend:latest
docker run -d --name algorithm-platform-frontend -p 3000:80 -v $(pwd)/frontend/public:/usr/share/nginx/html nginx:alpine
```

### 查看状态
```bash
# 查看运行中的容器
docker ps | grep algorithm-platform

# 查看容器日志
docker logs -f algorithm-platform-test
docker logs -f algorithm-platform-frontend

# 检查服务健康状态
curl http://localhost:8100/health
curl http://localhost:3000/
```

### 停止和清理
```bash
# 停止容器
docker stop algorithm-platform-test algorithm-platform-frontend

# 删除容器
docker rm algorithm-platform-test algorithm-platform-frontend

# 删除镜像
docker rmi algorithm-platform-backend:latest

# 一键清理所有相关资源
docker rm -f $(docker ps -aq --filter "name=algorithm-platform")
docker rmi $(docker images --filter "reference=algorithm-platform*" -q)
docker system prune -f
```

### 重启服务
```bash
# 重启单个容器
docker restart algorithm-platform-test

# 重启所有容器
docker restart $(docker ps -q --filter "name=algorithm-platform")

# 完全重新部署
docker-compose down
docker-compose up -d --build
```

## 授权服务集成

平台集成现有的授权认证服务 (`http://127.0.0.1:8000/`)，通过Web界面配置授权密钥，所有算法调用都使用统一的密钥进行认证。

### 配置步骤
1. 访问Web界面的"系统配置"页面
2. 输入授权密钥
3. 点击"验证"按钮测试连接
4. 保存配置后，所有API调用自动使用该密钥

## 故障排除

### 常见问题

**端口被占用**
```bash
# 检查端口占用
lsof -i :3000
lsof -i :8100

# 强制停止占用端口的进程
sudo kill -9 $(lsof -t -i:3000)
sudo kill -9 $(lsof -t -i:8100)
```

**容器启动失败**
```bash
# 查看详细错误日志
docker logs algorithm-platform-test
docker logs algorithm-platform-frontend

# 检查镜像是否存在
docker images | grep algorithm-platform

# 重新构建镜像
docker build -t algorithm-platform-backend:latest ./backend
```

**权限问题**
```bash
# 检查Docker权限
docker info

# 添加用户到docker组（Linux）
sudo usermod -aG docker $USER
newgrp docker
```

### 快速命令参考

```bash
# === 启动相关 ===
./start.sh development          # 启动开发环境
./start.sh production          # 启动生产环境
docker-compose up -d           # 使用compose启动

# === 状态检查 ===
docker ps | grep algorithm     # 查看容器状态
curl localhost:8100/health     # 检查后端健康
curl localhost:3000/          # 检查前端访问

# === 日志查看 ===
docker logs -f algorithm-platform-test      # 后端日志
docker logs -f algorithm-platform-frontend  # 前端日志

# === 停止清理 ===
docker stop $(docker ps -q --filter "name=algorithm-platform")  # 停止所有
docker rm $(docker ps -aq --filter "name=algorithm-platform")   # 删除所有
docker-compose down -v                                          # compose停止

# === 重启服务 ===
docker restart algorithm-platform-test      # 重启后端
docker restart algorithm-platform-frontend  # 重启前端

# === 完全重置 ===
docker-compose down -v
docker system prune -f
./build.sh development
./start.sh development
```

## 许可证

本项目遵循 MIT 许可证。

---

*开发团队: 算法管理平台开发组*
*最后更新: 2025-07-25*
