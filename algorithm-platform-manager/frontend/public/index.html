<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 32px;
        }
        
        .description {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            font-size: 14px;
        }
        
        .actions {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .status {
            margin-top: 30px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }
        
        .status h3 {
            color: #155724;
            margin-bottom: 10px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            color: #155724;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1>算法管理平台</h1>
        <p class="description">
            统一管理和调用AI算法容器的平台，支持动态加载算法包，提供标准化API接口和可视化管理界面。
        </p>
        
        <div class="features">
            <div class="feature">
                <h3>🔧 容器管理</h3>
                <p>动态启动/停止/重启算法容器，自动端口分配和健康检查</p>
            </div>
            <div class="feature">
                <h3>🌐 API网关</h3>
                <p>统一的API路由和代理转发，支持负载均衡和请求监控</p>
            </div>
            <div class="feature">
                <h3>🔐 授权认证</h3>
                <p>集成现有授权服务，统一密钥管理和权限控制</p>
            </div>
            <div class="feature">
                <h3>📊 实时监控</h3>
                <p>算法状态监控，系统资源监控和任务执行统计</p>
            </div>
        </div>
        
        <div class="actions">
            <a href="/api/docs" class="btn" target="_blank">API文档</a>
            <a href="javascript:void(0)" class="btn secondary" onclick="checkStatus()">检查状态</a>
        </div>
        
        <div class="status" id="status" style="display: none;">
            <h3>系统状态</h3>
            <div class="status-item">
                <span>后端API:</span>
                <span id="backend-status"><div class="loading"></div></span>
            </div>
            <div class="status-item">
                <span>数据库:</span>
                <span id="db-status"><div class="loading"></div></span>
            </div>
            <div class="status-item">
                <span>算法数量:</span>
                <span id="algorithm-count"><div class="loading"></div></span>
            </div>
        </div>
    </div>

    <script>
        async function checkStatus() {
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'block';
            
            // 重置状态
            document.getElementById('backend-status').innerHTML = '<div class="loading"></div>';
            document.getElementById('db-status').innerHTML = '<div class="loading"></div>';
            document.getElementById('algorithm-count').innerHTML = '<div class="loading"></div>';
            
            try {
                // 检查后端健康状态
                const healthResponse = await fetch('/api/system/health');
                const healthData = await healthResponse.json();
                
                if (healthData.success) {
                    document.getElementById('backend-status').innerHTML = '✅ 正常';
                    document.getElementById('db-status').innerHTML = 
                        healthData.data.components.database === 'healthy' ? '✅ 正常' : '❌ 异常';
                } else {
                    document.getElementById('backend-status').innerHTML = '❌ 异常';
                    document.getElementById('db-status').innerHTML = '❌ 异常';
                }
                
                // 获取算法数量
                const algorithmsResponse = await fetch('/api/algorithms/');
                const algorithmsData = await algorithmsResponse.json();
                
                if (algorithmsData.success) {
                    document.getElementById('algorithm-count').innerHTML = 
                        `${algorithmsData.total} 个`;
                } else {
                    document.getElementById('algorithm-count').innerHTML = '❌ 获取失败';
                }
                
            } catch (error) {
                console.error('检查状态失败:', error);
                document.getElementById('backend-status').innerHTML = '❌ 连接失败';
                document.getElementById('db-status').innerHTML = '❌ 连接失败';
                document.getElementById('algorithm-count').innerHTML = '❌ 连接失败';
            }
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
