# 前端开发文档

## 🎯 项目概述

算法管理平台前端是一个基于Vue 3的现代化Web应用，提供算法容器管理、在线测试和结果可视化功能。

## 🏗️ 技术架构

### 核心技术栈
- **框架**: Vue 3.4+ (Composition API)
- **构建工具**: Vite 5.0+
- **UI组件库**: Element Plus 2.4+
- **HTTP客户端**: Axios 1.6+
- **路由管理**: Vue Router 4.0+
- **开发语言**: JavaScript (ES2022)

### 项目结构
```
frontend/
├── src/
│   ├── components/          # 可复用组件
│   ├── views/              # 页面组件
│   │   ├── Dashboard.vue   # 仪表盘
│   │   ├── Algorithms.vue  # 算法管理
│   │   └── Test.vue        # 在线测试 (核心)
│   ├── api/                # API接口
│   ├── utils/              # 工具函数
│   ├── assets/             # 静态资源
│   └── main.js             # 应用入口
├── public/                 # 公共资源
└── package.json           # 项目配置
```

## 🎨 核心功能实现

### 1. 算法管理界面 (Algorithms.vue)

#### 功能特性
- 实时显示Docker容器状态
- 算法信息展示（版本、类型、端口）
- 一键进入测试界面

#### 关键代码
```javascript
// 获取算法列表
const fetchAlgorithms = async () => {
  try {
    const response = await axios.get('/api/algorithms')
    algorithms.value = response.data
  } catch (error) {
    ElMessage.error('获取算法列表失败')
  }
}

// 状态标识
const getStatusType = (status) => {
  return status === 'running' ? 'success' : 'danger'
}
```

### 2. 在线测试功能 (Test.vue)

#### 核心组件结构
```javascript
// 响应式数据
const selectedAlgorithm = ref(null)
const selectedFunction = ref(null)
const uploadedFiles = ref([])
const testResult = ref(null)
const isLoading = ref(false)

// 生命周期
onMounted(() => {
  fetchAlgorithms()
  initializeCanvas()
})
```

#### 文件上传处理
```javascript
const handleFileUpload = (file) => {
  // 文件类型验证
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 JPG、PNG 格式的图片')
    return false
  }
  
  // 文件大小限制
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    ElMessage.error('图片大小不能超过 10MB')
    return false
  }
  
  uploadedFiles.value.push(file)
  return false // 阻止自动上传
}
```

### 3. 结果可视化系统

#### 绘制引擎核心实现
```javascript
// 自动绘制函数
const drawResultOnImages = () => {
  if (!testResult.value?.success) return
  
  const resultData = testResult.value.data?.result?.response?.data
  if (!resultData) return
  
  // 根据算法类型分发绘制
  const functionName = selectedFunction.value?.name
  if (functionName === 'Detect Faces' && resultData.faces) {
    drawFaceDetectionResults(resultData)
  } else if (functionName === 'Compare Faces' && resultData.faces) {
    drawFaceComparisonResults(resultData)
  } else if (functionName === 'Assess Face Quality' && resultData.faces) {
    drawFaceQualityResults(resultData)
  }
}

// 人脸检测结果绘制
const drawFaceDetectionResults = (resultData) => {
  const canvases = document.querySelectorAll('.result-overlay')
  const images = document.querySelectorAll('.preview-image')
  
  canvases.forEach((canvas, index) => {
    const img = images[index]
    if (!canvas || !img) return
    
    const ctx = canvas.getContext('2d')
    
    // 等待图片加载完成
    if (!img.complete || img.naturalWidth === 0) {
      img.onload = () => drawOnCanvas(canvas, img, ctx, resultData)
    } else {
      drawOnCanvas(canvas, img, ctx, resultData)
    }
  })
}

// Canvas绘制核心逻辑
const drawOnCanvas = (canvas, img, ctx, resultData) => {
  // 设置Canvas尺寸与图片一致
  canvas.width = img.naturalWidth
  canvas.height = img.naturalHeight
  canvas.style.width = img.offsetWidth + 'px'
  canvas.style.height = img.offsetHeight + 'px'
  
  // 清除之前的绘制
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制人脸检测结果
  if (resultData.faces && resultData.faces.length > 0) {
    resultData.faces.forEach((face, faceIndex) => {
      if (face.bbox) {
        // 正确解析bbox格式 [x1, y1, x2, y2, confidence]
        const [x1, y1, x2, y2, confidence] = face.bbox
        const x = x1, y = y1
        const width = x2 - x1, height = y2 - y1
        
        // 绘制红色边界框
        ctx.strokeStyle = '#ff0000'
        ctx.lineWidth = 2
        ctx.strokeRect(x, y, width, height)
        
        // 绘制置信度标签
        if (confidence > 0) {
          const confidenceText = `${(confidence * 100).toFixed(1)}%`
          ctx.font = '14px Arial'
          ctx.fillStyle = '#ff0000'
          ctx.fillText(confidenceText, x, y - 5)
        }
        
        // 绘制关键点
        if (face.landmarks && Array.isArray(face.landmarks)) {
          ctx.fillStyle = '#ff0000'
          face.landmarks.forEach(([lx, ly]) => {
            ctx.beginPath()
            ctx.arc(lx, ly, 2, 0, 2 * Math.PI)
            ctx.fill()
          })
        }
      }
    })
  }
}
```

## 🔧 关键技术难点

### 1. bbox数据格式兼容性

**问题**: API返回的bbox格式为 `[x1, y1, x2, y2, confidence]`，容易与 `[x, y, width, height, confidence]` 格式混淆。

**解决方案**:
```javascript
// 正确的坐标转换
const [x1, y1, x2, y2, confidence] = face.bbox
const x = x1, y = y1
const width = x2 - x1, height = y2 - y1
```

### 2. Canvas尺寸适配

**问题**: Canvas默认尺寸与图片不匹配，导致绘制位置偏移。

**解决方案**:
```javascript
// 动态设置Canvas尺寸
canvas.width = img.naturalWidth    // 实际像素尺寸
canvas.height = img.naturalHeight
canvas.style.width = img.offsetWidth + 'px'   // 显示尺寸
canvas.style.height = img.offsetHeight + 'px'
```

### 3. 异步绘制时序

**问题**: 图片可能未加载完成就开始绘制，导致绘制失败。

**解决方案**:
```javascript
// 检查图片加载状态
if (!img.complete || img.naturalWidth === 0) {
  img.onload = () => drawOnCanvas(canvas, img, ctx, resultData)
} else {
  drawOnCanvas(canvas, img, ctx, resultData)
}
```

### 4. 多算法结果适配

**问题**: 不同算法返回的数据结构和绘制需求不同。

**解决方案**:
```javascript
// 算法分发机制
const functionName = selectedFunction.value?.name
if (functionName === 'Detect Faces') {
  drawFaceDetectionResults(resultData)
} else if (functionName === 'Compare Faces') {
  drawFaceComparisonResults(resultData)
} else if (functionName === 'Assess Face Quality') {
  drawFaceQualityResults(resultData)
}
```

## 📊 性能优化

### 1. Canvas优化
- 动态调整Canvas尺寸，避免内存浪费
- 使用 `clearRect` 清除之前的绘制，避免重叠
- 批量绘制操作，减少重绘次数

### 2. 图片处理优化
- 文件大小限制（10MB）
- 支持的格式限制（JPG/PNG/JPEG）
- 图片预览缓存

### 3. 网络请求优化
- 使用Axios拦截器统一处理错误
- 请求去重和缓存
- 加载状态管理

## 🧪 测试策略

### 单元测试
- 绘制函数测试
- 数据处理函数测试
- 工具函数测试

### 集成测试
- API接口测试
- 文件上传测试
- 结果展示测试

### E2E测试
- 完整流程测试
- 多浏览器兼容性测试
- 响应式布局测试

## 🚀 部署配置

### 开发环境
```bash
npm run dev    # 启动开发服务器 (http://localhost:3000)
```

### 生产构建
```bash
npm run build  # 构建生产版本到 dist/ 目录
```

### 环境变量
```javascript
// vite.config.js
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8001',
        changeOrigin: true
      }
    }
  }
})
```

## 📋 开发规范

### 代码风格
- 使用ES6+语法
- 组件采用Composition API
- 统一的命名规范（camelCase）
- 详细的注释说明

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

---

**这份文档记录了前端开发的核心技术实现，为后续开发和维护提供重要参考！** 📚
