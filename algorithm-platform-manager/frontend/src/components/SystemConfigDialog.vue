<template>
  <el-dialog
    v-model="visible"
    title="系统配置"
    width="600px"
    @close="handleClose"
  >
    <el-form :model="config" label-width="120px">
      <el-form-item label="系统名称">
        <el-input v-model="config.systemName" />
      </el-form-item>
      
      <el-form-item label="API端口">
        <el-input-number v-model="config.apiPort" :min="1000" :max="65535" />
      </el-form-item>
      
      <el-form-item label="日志级别">
        <el-select v-model="config.logLevel">
          <el-option label="DEBUG" value="debug" />
          <el-option label="INFO" value="info" />
          <el-option label="WARNING" value="warning" />
          <el-option label="ERROR" value="error" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="自动刷新">
        <el-switch v-model="config.autoRefresh" />
      </el-form-item>
      
      <el-form-item label="刷新间隔(秒)">
        <el-input-number 
          v-model="config.refreshInterval" 
          :min="5" 
          :max="300" 
          :disabled="!config.autoRefresh"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'SystemConfigDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'updated'],
  setup(props, { emit }) {
    const visible = ref(props.modelValue)
    
    const config = reactive({
      systemName: '算法管理平台',
      apiPort: 8100,
      logLevel: 'info',
      autoRefresh: true,
      refreshInterval: 30
    })

    watch(() => props.modelValue, (val) => {
      visible.value = val
    })

    watch(visible, (val) => {
      emit('update:modelValue', val)
    })

    const handleClose = () => {
      visible.value = false
    }

    const handleSave = () => {
      ElMessage.success('配置保存成功')
      emit('updated', config)
      handleClose()
    }

    return {
      visible,
      config,
      handleClose,
      handleSave
    }
  }
}
</script>
