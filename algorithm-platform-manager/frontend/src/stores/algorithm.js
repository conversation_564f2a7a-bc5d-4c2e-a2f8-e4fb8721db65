import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/api'

export const useAlgorithmStore = defineStore('algorithm', () => {
  const algorithms = ref([])
  const loading = ref(false)
  const error = ref(null)

  // 获取算法列表
  const fetchAlgorithms = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.get('/api/algorithms/')
      if (response.data.success) {
        algorithms.value = response.data.algorithms
      } else {
        error.value = response.data.message
      }
    } catch (err) {
      error.value = err.message || '获取算法列表失败'
      console.error('获取算法列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 上传算法包
  const uploadAlgorithm = async (file) => {
    const formData = new FormData()
    formData.append('file', file)
    
    try {
      const response = await api.post('/api/algorithms/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      if (response.data.success) {
        await fetchAlgorithms() // 刷新列表
        return response.data
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      throw new Error(err.response?.data?.detail || err.message || '上传失败')
    }
  }

  // 启动算法
  const startAlgorithm = async (name) => {
    try {
      const response = await api.post(`/api/algorithms/${name}/start`)
      if (response.data.success) {
        await fetchAlgorithms() // 刷新列表
        return response.data
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      throw new Error(err.response?.data?.detail || err.message || '启动失败')
    }
  }

  // 停止算法
  const stopAlgorithm = async (name) => {
    try {
      const response = await api.post(`/api/algorithms/${name}/stop`)
      if (response.data.success) {
        await fetchAlgorithms() // 刷新列表
        return response.data
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      throw new Error(err.response?.data?.detail || err.message || '停止失败')
    }
  }

  // 卸载算法
  const unloadAlgorithm = async (name) => {
    try {
      const response = await api.delete(`/api/algorithms/${name}`)
      if (response.data.success) {
        await fetchAlgorithms() // 刷新列表
        return response.data
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      throw new Error(err.response?.data?.detail || err.message || '卸载失败')
    }
  }

  return {
    algorithms,
    loading,
    error,
    fetchAlgorithms,
    uploadAlgorithm,
    startAlgorithm,
    stopAlgorithm,
    unloadAlgorithm
  }
})
