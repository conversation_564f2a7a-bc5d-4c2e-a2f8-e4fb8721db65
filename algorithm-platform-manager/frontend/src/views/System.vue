<template>
  <div class="system-page">
    <el-card class="page-header">
      <template #header>
        <div class="card-header">
          <span>⚙️ 系统管理</span>
          <el-button type="primary" @click="refreshData">
            <el-icon><RefreshIcon /></el-icon>
            刷新数据
          </el-button>
        </div>
      </template>
    </el-card>

    <!-- 系统统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon cpu">
              <el-icon><CpuIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ systemStats.cpu_usage }}%</div>
              <div class="stat-label">CPU使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon memory">
              <el-icon><MemoryIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ systemStats.memory_usage }}%</div>
              <div class="stat-label">内存使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon disk">
              <el-icon><HardDriveIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ systemStats.disk_usage }}%</div>
              <div class="stat-label">磁盘使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon algorithms">
              <el-icon><DataAnalysisIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ systemStats.total_algorithms }}</div>
              <div class="stat-label">算法总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card title="系统信息">
          <template #header>
            <span>系统信息</span>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="环境">
              {{ systemStats.environment }}
            </el-descriptions-item>
            <el-descriptions-item label="Python版本">
              {{ systemStats.python_version }}
            </el-descriptions-item>
            <el-descriptions-item label="平台">
              {{ systemStats.platform }}
            </el-descriptions-item>
            <el-descriptions-item label="系统运行时间">
              {{ systemStats.system_uptime }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card title="任务统计">
          <template #header>
            <span>任务统计</span>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="总任务数">
              {{ systemStats.total_tasks }}
            </el-descriptions-item>
            <el-descriptions-item label="成功任务">
              <el-tag type="success">{{ systemStats.successful_tasks }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="失败任务">
              <el-tag type="danger">{{ systemStats.failed_tasks }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="运行中算法">
              <el-tag type="warning">{{ systemStats.running_algorithms }}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统日志 -->
    <el-card class="logs-card">
      <template #header>
        <div class="card-header">
          <span>📋 系统日志</span>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>
      <div class="logs-container">
        <div v-if="systemLogs.length === 0" class="no-logs">
          暂无系统日志
        </div>
        <div v-else class="logs-content">
          <div
            v-for="log in systemLogs"
            :key="log.id"
            :class="['log-item', `log-${log.level}`]"
          >
            <span class="log-time">{{ log.timestamp }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh as RefreshIcon,
  Cpu as CpuIcon,
  Monitor as MemoryIcon,
  Folder as HardDriveIcon,
  DataAnalysis as DataAnalysisIcon
} from '@element-plus/icons-vue'

export default {
  name: 'SystemPage',
  components: {
    RefreshIcon,
    CpuIcon,
    MemoryIcon,
    HardDriveIcon,
    DataAnalysisIcon
  },
  setup() {
    const loading = ref(false)
    const systemStats = reactive({
      cpu_usage: 0,
      memory_usage: 0,
      disk_usage: 0,
      total_algorithms: 0,
      running_algorithms: 0,
      total_tasks: 0,
      successful_tasks: 0,
      failed_tasks: 0,
      environment: 'uv虚拟环境专业版',
      python_version: 'Python 3.11',
      platform: 'darwin',
      system_uptime: '运行中'
    })

    const systemLogs = reactive([
      {
        id: 1,
        timestamp: new Date().toLocaleString(),
        level: 'info',
        message: '算法管理平台专业版启动成功'
      },
      {
        id: 2,
        timestamp: new Date().toLocaleString(),
        level: 'info',
        message: 'uv虚拟环境初始化完成'
      },
      {
        id: 3,
        timestamp: new Date().toLocaleString(),
        level: 'success',
        message: '所有系统组件运行正常'
      }
    ])

    const fetchSystemStats = async () => {
      try {
        const response = await fetch('/api/system/stats')
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            Object.assign(systemStats, data.stats)
          }
        }
      } catch (error) {
        console.error('获取系统统计失败:', error)
        ElMessage.error('获取系统统计失败')
      }
    }

    const refreshData = async () => {
      loading.value = true
      try {
        await fetchSystemStats()
        ElMessage.success('数据刷新成功')
        
        // 添加刷新日志
        systemLogs.unshift({
          id: Date.now(),
          timestamp: new Date().toLocaleString(),
          level: 'info',
          message: '系统数据刷新完成'
        })
      } catch (error) {
        ElMessage.error('数据刷新失败')
      } finally {
        loading.value = false
      }
    }

    const clearLogs = () => {
      systemLogs.splice(0, systemLogs.length)
      ElMessage.success('日志已清空')
    }

    onMounted(() => {
      fetchSystemStats()
    })

    return {
      loading,
      systemStats,
      systemLogs,
      refreshData,
      clearLogs
    }
  }
}
</script>

<style scoped>
.system-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.cpu {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.memory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.disk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.algorithms {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.logs-card {
  margin-top: 20px;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
}

.no-logs {
  text-align: center;
  color: #909399;
  padding: 40px 0;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  width: 150px;
  font-size: 12px;
  color: #909399;
}

.log-level {
  width: 80px;
  font-size: 12px;
  font-weight: bold;
}

.log-message {
  flex: 1;
  font-size: 14px;
}

.log-info .log-level {
  color: #409eff;
}

.log-success .log-level {
  color: #67c23a;
}

.log-warning .log-level {
  color: #e6a23c;
}

.log-error .log-level {
  color: #f56c6c;
}

.el-card {
  margin-bottom: 20px;
}
</style>
