<template>
  <div class="tasks-page">
    <el-card class="page-header">
      <template #header>
        <div class="card-header">
          <span>📋 任务管理</span>
          <el-button type="primary" @click="refreshTasks">
            <el-icon><RefreshIcon /></el-icon>
            刷新列表
          </el-button>
        </div>
      </template>
    </el-card>

    <!-- 任务统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><DocumentIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ taskStats.total }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon running">
              <el-icon><LoadingIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ taskStats.running }}</div>
              <div class="stat-label">运行中</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><SuccessFilledIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ taskStats.success }}</div>
              <div class="stat-label">成功</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon failed">
              <el-icon><CircleCloseFilledIcon /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ taskStats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>任务列表</span>
          <div>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-right: 10px">
              <el-option label="全部" value="" />
              <el-option label="运行中" value="running" />
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="等待中" value="pending" />
            </el-select>
            <el-input
              v-model="searchText"
              placeholder="搜索任务..."
              style="width: 200px"
              clearable
            >
              <template #prefix>
                <el-icon><SearchIcon /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredTasks"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="任务ID" width="120" />
        <el-table-column prop="name" label="任务名称" />
        <el-table-column prop="algorithm" label="算法" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.progress"
              :status="scope.row.status === 'failed' ? 'exception' : undefined"
              :stroke-width="6"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_time" label="创建时间" width="160" />
        <el-table-column prop="duration" label="耗时" width="100" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="viewTask(scope.row)"
            >
              查看
            </el-button>
            <el-button
              v-if="scope.row.status === 'running'"
              size="small"
              type="warning"
              @click="stopTask(scope.row)"
            >
              停止
            </el-button>
            <el-button
              v-else-if="scope.row.status === 'failed'"
              size="small"
              type="success"
              @click="retryTask(scope.row)"
            >
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="tasks.length"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="`任务详情 - ${selectedTask?.name}`"
      width="800px"
    >
      <div v-if="selectedTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">
            {{ selectedTask.id }}
          </el-descriptions-item>
          <el-descriptions-item label="任务名称">
            {{ selectedTask.name }}
          </el-descriptions-item>
          <el-descriptions-item label="算法">
            {{ selectedTask.algorithm }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">
            {{ selectedTask.progress }}%
          </el-descriptions-item>
          <el-descriptions-item label="耗时">
            {{ selectedTask.duration }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ selectedTask.created_time }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ selectedTask.completed_time || '未完成' }}
          </el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>任务日志</h4>
          <el-input
            type="textarea"
            :rows="10"
            v-model="taskLogs"
            readonly
            placeholder="任务日志将显示在这里..."
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="refreshLogs">刷新日志</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Document as DocumentIcon,
  Loading as LoadingIcon,
  SuccessFilled as SuccessFilledIcon,
  CircleCloseFilled as CircleCloseFilledIcon
} from '@element-plus/icons-vue'

export default {
  name: 'TasksPage',
  components: {
    RefreshIcon,
    SearchIcon,
    DocumentIcon,
    LoadingIcon,
    SuccessFilledIcon,
    CircleCloseFilledIcon
  },
  setup() {
    const loading = ref(false)
    const searchText = ref('')
    const statusFilter = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const dialogVisible = ref(false)
    const selectedTask = ref(null)
    const taskLogs = ref('')

    const taskStats = reactive({
      total: 15,
      running: 2,
      success: 10,
      failed: 3
    })

    const tasks = reactive([
      {
        id: 'task-001',
        name: '图像识别任务',
        algorithm: '图像识别算法',
        status: 'running',
        progress: 65,
        created_time: '2024-01-15 10:30:00',
        duration: '5分钟',
        completed_time: null
      },
      {
        id: 'task-002',
        name: '文本分析任务',
        algorithm: '文本分析算法',
        status: 'success',
        progress: 100,
        created_time: '2024-01-15 09:15:00',
        duration: '3分钟',
        completed_time: '2024-01-15 09:18:00'
      },
      {
        id: 'task-003',
        name: '数据处理任务',
        algorithm: '数据处理算法',
        status: 'failed',
        progress: 30,
        created_time: '2024-01-15 08:45:00',
        duration: '2分钟',
        completed_time: '2024-01-15 08:47:00'
      },
      {
        id: 'task-004',
        name: '模型训练任务',
        algorithm: '机器学习算法',
        status: 'pending',
        progress: 0,
        created_time: '2024-01-15 11:00:00',
        duration: '0分钟',
        completed_time: null
      }
    ])

    const filteredTasks = computed(() => {
      let filtered = tasks

      if (statusFilter.value) {
        filtered = filtered.filter(task => task.status === statusFilter.value)
      }

      if (searchText.value) {
        filtered = filtered.filter(task =>
          task.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
          task.algorithm.toLowerCase().includes(searchText.value.toLowerCase())
        )
      }

      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filtered.slice(start, end)
    })

    const getStatusType = (status) => {
      const statusMap = {
        running: 'warning',
        success: 'success',
        failed: 'danger',
        pending: 'info'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        running: '运行中',
        success: '成功',
        failed: '失败',
        pending: '等待中'
      }
      return statusMap[status] || status
    }

    const refreshTasks = async () => {
      loading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        ElMessage.success('任务列表刷新成功')
      } catch (error) {
        ElMessage.error('刷新任务列表失败')
      } finally {
        loading.value = false
      }
    }

    const viewTask = (task) => {
      selectedTask.value = task
      taskLogs.value = `[${new Date().toLocaleString()}] 任务 ${task.name} 开始执行\n[${new Date().toLocaleString()}] 正在处理数据...\n[${new Date().toLocaleString()}] 当前进度: ${task.progress}%`
      dialogVisible.value = true
    }

    const stopTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          `确定要停止任务 "${task.name}" 吗？`,
          '确认停止',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        task.status = 'failed'
        task.progress = task.progress
        ElMessage.success(`任务 "${task.name}" 已停止`)
      } catch {
        // 用户取消
      }
    }

    const retryTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          `确定要重试任务 "${task.name}" 吗？`,
          '确认重试',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )

        task.status = 'running'
        task.progress = 0
        ElMessage.success(`任务 "${task.name}" 开始重试`)
        
        // 模拟任务进度
        const interval = setInterval(() => {
          if (task.progress < 100) {
            task.progress += 10
          } else {
            task.status = 'success'
            clearInterval(interval)
            ElMessage.success(`任务 "${task.name}" 重试成功`)
          }
        }, 1000)
      } catch {
        // 用户取消
      }
    }

    const refreshLogs = () => {
      if (selectedTask.value) {
        taskLogs.value += `\n[${new Date().toLocaleString()}] 日志已刷新`
        ElMessage.success('日志刷新成功')
      }
    }

    onMounted(() => {
      // 初始化时可以加载任务数据
    })

    return {
      loading,
      searchText,
      statusFilter,
      currentPage,
      pageSize,
      dialogVisible,
      selectedTask,
      taskLogs,
      taskStats,
      tasks,
      filteredTasks,
      getStatusType,
      getStatusText,
      refreshTasks,
      viewTask,
      stopTask,
      retryTask,
      refreshLogs
    }
  }
}
</script>

<style scoped>
.tasks-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.running {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-card {
  margin-bottom: 20px;
}
</style>
