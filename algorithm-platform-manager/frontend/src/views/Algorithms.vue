<template>
  <div class="algorithms-page">
    <!-- 算法管理 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🤖 算法管理</span>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索算法..."
              style="width: 200px; margin-right: 12px"
              clearable
            >
              <template #prefix>
                <el-icon><SearchIcon /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="refreshAlgorithms" :loading="loading">
              <el-icon><RefreshIcon /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredAlgorithms"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="算法名称" width="200" />
        <el-table-column prop="description" label="描述" width="300" />
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="ports" label="端口" width="200" />
        <el-table-column prop="status" label="运行状态" width="120">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="viewAlgorithm(scope.row)"
            >
              查看
            </el-button>
            <el-button
              size="small"
              :type="scope.row.status === 'running' ? 'danger' : 'success'"
              :disabled="scope.row.status === 'pending' || loading"
              @click="toggleAlgorithm(scope.row)"
              :loading="scope.row.loading"
            >
              {{ scope.row.status === 'running' ? '停止' : '启动' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="algorithms.length"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>

    <!-- 算法详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="selectedAlgorithm?.name"
      width="600px"
    >
      <div v-if="selectedAlgorithm">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">
            {{ selectedAlgorithm.id }}
          </el-descriptions-item>
          <el-descriptions-item label="版本">
            {{ selectedAlgorithm.version }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedAlgorithm.status)">
              {{ getStatusText(selectedAlgorithm.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="环境">
            {{ selectedAlgorithm.environment }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedAlgorithm.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="runAlgorithm(selectedAlgorithm)"
          :disabled="selectedAlgorithm?.status !== 'ready'"
        >
          运行算法
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh as RefreshIcon,
  Search as SearchIcon
} from '@element-plus/icons-vue'

export default {
  name: 'AlgorithmsPage',
  components: {
    RefreshIcon,
    SearchIcon
  },
  setup() {
    const loading = ref(false)
    const searchText = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const dialogVisible = ref(false)
    const selectedAlgorithm = ref(null)

    const algorithms = reactive([])

    const filteredAlgorithms = computed(() => {
      let filtered = algorithms
      
      if (searchText.value) {
        filtered = algorithms.filter(algo =>
          algo.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
          algo.description.toLowerCase().includes(searchText.value.toLowerCase())
        )
      }

      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filtered.slice(start, end)
    })

    const getStatusType = (status) => {
      const statusMap = {
        ready: 'success',
        running: 'warning',
        stopped: 'info',
        error: 'danger'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        ready: '就绪',
        running: '运行中',
        stopped: '已停止',
        error: '错误',
        built: '已构建',
        starting: '启动中',
        unhealthy: '不健康',
        uploaded: '已上传',
        build_failed: '构建失败',
        start_failed: '启动失败'
      }
      return statusMap[status] || '未知'
    }

    const fetchAlgorithms = async () => {
      loading.value = true
      try {
        const response = await fetch('/api/algorithms/')
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            algorithms.splice(0, algorithms.length, ...data.algorithms)
          }
        }
      } catch (error) {
        console.error('获取算法列表失败:', error)
        ElMessage.error('获取算法列表失败')
      } finally {
        loading.value = false
      }
    }



    const refreshAlgorithms = async () => {
      loading.value = true
      try {
        const response = await fetch('/api/algorithms/scan', {
          method: 'POST'
        })
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            algorithms.splice(0, algorithms.length, ...data.algorithms)
            ElMessage.success(data.message)
          } else {
            ElMessage.error(data.message || '刷新失败')
          }
        }
      } catch (error) {
        console.error('刷新算法列表失败:', error)
        ElMessage.error('刷新算法列表失败')
      } finally {
        loading.value = false
      }
    }

    const viewAlgorithm = (algorithm) => {
      selectedAlgorithm.value = algorithm
      dialogVisible.value = true
    }

    const toggleAlgorithm = async (algorithm) => {
      try {
        const isRunning = algorithm.status === 'running'
        const action = isRunning ? '停止' : '启动'

        await ElMessageBox.confirm(
          `确定要${action}算法 "${algorithm.name}" 吗？`,
          `确认${action}`,
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 设置加载状态
        algorithm.loading = true

        try {
          const endpoint = isRunning ? 'stop' : 'start'
          const response = await fetch(`/api/algorithms/${algorithm.id}/${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          })

          const result = await response.json()

          if (result.success) {
            algorithm.status = result.status
            ElMessage.success(result.message)
            // 刷新列表以获取最新状态
            await fetchAlgorithms()
          } else {
            throw new Error(result.detail || `${action}失败`)
          }
        } catch (error) {
          ElMessage.error(`${action}算法失败: ${error.message}`)
        } finally {
          algorithm.loading = false
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error(`${algorithm.status === 'running' ? '停止' : '启动'}算法失败`)
        }
      }
    }

    const runAlgorithm = async (algorithm) => {
      // 保留原有的运行方法，用于详情对话框
      await toggleAlgorithm(algorithm)
    }

    onMounted(() => {
      fetchAlgorithms()
    })

    return {
      loading,
      searchText,
      currentPage,
      pageSize,
      dialogVisible,
      selectedAlgorithm,
      algorithms,
      filteredAlgorithms,
      getStatusType,
      getStatusText,
      fetchAlgorithms,
      refreshAlgorithms,
      viewAlgorithm,
      runAlgorithm,
      toggleAlgorithm
    }
  }
}
</script>

<style scoped>
.algorithms-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-card {
  margin-bottom: 20px;
}
</style>
