<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#409EFF"><Box /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.total_algorithms }}</div>
              <div class="stat-label">总算法数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#67C23A"><VideoPlay /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.running_algorithms }}</div>
              <div class="stat-label">运行中</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#E6A23C"><List /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.total_tasks }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#F56C6C"><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.successful_tasks }}</div>
              <div class="stat-label">成功任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 系统资源 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统资源</span>
          </template>
          <div class="resource-item">
            <span>CPU使用率</span>
            <el-progress :percentage="dashboardStats.cpu_usage" :color="getProgressColor(dashboardStats.cpu_usage)" />
          </div>
          <div class="resource-item">
            <span>内存使用率</span>
            <el-progress :percentage="dashboardStats.memory_usage" :color="getProgressColor(dashboardStats.memory_usage)" />
          </div>
          <div class="resource-item">
            <span>磁盘使用率</span>
            <el-progress :percentage="dashboardStats.disk_usage" :color="getProgressColor(dashboardStats.disk_usage)" />
          </div>
        </el-card>
      </el-col>

      <!-- 最近活动 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最近活动</span>
            <el-button type="primary" size="small" style="float: right;" @click="refreshStats">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.timestamp"
              :type="activity.type"
            >
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/algorithms')">
              <el-icon><Box /></el-icon>
              管理算法
            </el-button>
            <el-button type="success" @click="$router.push('/test')">
              <el-icon><Tools /></el-icon>
              在线测试
            </el-button>
            <el-button type="info" @click="$router.push('/system')">
              <el-icon><Setting /></el-icon>
              系统配置
            </el-button>
            <el-button type="warning" @click="$router.push('/tasks')">
              <el-icon><List /></el-icon>
              任务历史
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Box,
  VideoPlay,
  List,
  Timer,
  Refresh
} from '@element-plus/icons-vue'

const dashboardStats = reactive({
  total_algorithms: 3,
  running_algorithms: 1,
  total_tasks: 15,
  successful_tasks: 12,
  failed_tasks: 3,
  cpu_usage: 25,
  memory_usage: 45,
  disk_usage: 60
})

const recentActivities = reactive([
  {
    id: 1,
    title: '算法管理平台启动',
    description: 'uv虚拟环境专业版成功启动',
    timestamp: new Date().toLocaleString(),
    type: 'success'
  },
  {
    id: 2,
    title: '前端构建完成',
    description: 'Vue 3 + Vite 前端构建成功',
    timestamp: new Date(Date.now() - 300000).toLocaleString(),
    type: 'primary'
  },
  {
    id: 3,
    title: '后端服务就绪',
    description: 'FastAPI后端服务启动完成',
    timestamp: new Date(Date.now() - 600000).toLocaleString(),
    type: 'success'
  }
])

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await fetch('/api/system/stats')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        Object.assign(dashboardStats, data.stats)
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 刷新数据
const refreshStats = async () => {
  await fetchStats()
  ElMessage.success('统计数据刷新成功')

  // 添加刷新活动
  recentActivities.unshift({
    id: Date.now(),
    title: '数据刷新',
    description: '仪表板数据已更新',
    timestamp: new Date().toLocaleString(),
    type: 'info'
  })
}

const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 40px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.resource-item {
  margin-bottom: 16px;
}

.resource-item span {
  display: inline-block;
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 120px;
}
</style>
