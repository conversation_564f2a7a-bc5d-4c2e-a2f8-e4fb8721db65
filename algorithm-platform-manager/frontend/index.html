<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="算法管理平台 - 统一管理和调用AI算法容器的专业平台" />
  <meta name="keywords" content="算法管理,AI,容器,Docker,API网关,uv虚拟环境" />
  <title>算法管理平台 - 专业版</title>

  <!-- 预加载关键资源 -->
  <link rel="preconnect" href="http://localhost:8100">

  <!-- 全局样式 -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }

    #app {
      min-height: 100vh;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: white;
      font-size: 18px;
    }

    .loading::after {
      content: '';
      width: 20px;
      height: 20px;
      border: 2px solid #ffffff;
      border-top: 2px solid transparent;
      border-radius: 50%;
      margin-left: 10px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="loading">
      🚀 算法管理平台专业版加载中...
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>
