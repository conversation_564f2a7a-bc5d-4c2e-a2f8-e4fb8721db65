# 算法管理平台专业版Dockerfile
# 多阶段构建：前端构建 -> 后端构建 -> 生产运行

# 阶段1: 前端构建环境
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# 复制前端依赖文件
COPY frontend/package*.json ./

# 安装依赖，使用更兼容的方式
RUN npm install --legacy-peer-deps --no-optional

# 复制前端源码
COPY frontend/ ./

# 构建前端，处理可能的构建错误
RUN npm run build

# 阶段2: Python后端构建环境
FROM python:3.11-slim AS backend-builder

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install uv

# 复制后端项目文件
COPY backend/pyproject.toml ./
COPY backend/requirements.txt ./
COPY backend/src/ ./src/

# 创建虚拟环境并安装依赖
RUN uv venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN uv pip install -r requirements.txt

# 阶段3: 生产运行环境
FROM python:3.11-slim AS production

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    nginx \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# 安装Docker CLI（用于管理算法容器）
RUN curl -fsSL https://get.docker.com -o get-docker.sh && \
    sh get-docker.sh && \
    rm get-docker.sh

# 安装Python依赖
COPY backend/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端代码
COPY backend/src/ ./src/

# 复制虚拟环境
COPY --from=backend-builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 复制应用代码
COPY --from=backend-builder /app/src ./src

# 复制前端构建结果
COPY --from=frontend-builder /app/frontend/dist ./static/

# 创建启动脚本
RUN cat > start.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 启动算法管理平台..."

# 启动后端API服务
python src/main.py --host 0.0.0.0 --port 8100 &
BACKEND_PID=$!

# 启动前端静态文件服务
python -m http.server 3000 --directory static &
FRONTEND_PID=$!

echo "✅ 后端API服务启动 (PID: $BACKEND_PID)"
echo "✅ 前端服务启动 (PID: $FRONTEND_PID)"
echo "📚 API文档: http://localhost:8100/docs"
echo "🌐 前端界面: http://localhost:3000"

# 等待进程
wait $BACKEND_PID $FRONTEND_PID
EOF

RUN chmod +x start.sh

# 创建必要的目录
RUN mkdir -p /app/data/algorithms /app/data/uploads /app/logs

# 设置权限
RUN useradd -m -u 1000 appuser && \
    chown -R appuser:appuser /app

# 暴露端口
EXPOSE 3000 8100

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8100/health && curl -f http://localhost:3000/ || exit 1

# 启动命令
CMD ["./start.sh"]
