#!/bin/bash

# 算法管理平台构建脚本

set -e

echo "=========================================="
echo "算法管理平台构建脚本"
echo "=========================================="

# 检查参数
BUILD_TYPE=${1:-"development"}
PUSH_IMAGE=${2:-"false"}

echo "构建类型: $BUILD_TYPE"
echo "推送镜像: $PUSH_IMAGE"

# 设置镜像标签
IMAGE_NAME="algorithm-platform"
IMAGE_TAG="latest"

if [ "$BUILD_TYPE" = "production" ]; then
    IMAGE_TAG="v1.0.0"
fi

FULL_IMAGE_NAME="$IMAGE_NAME:$IMAGE_TAG"

echo "镜像名称: $FULL_IMAGE_NAME"

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装或不可用"
    exit 1
fi

# 清理旧的构建文件
echo "清理旧的构建文件..."
rm -rf frontend/dist
rm -rf backend/__pycache__
rm -rf backend/src/__pycache__

# 构建前端（如果是生产环境）
if [ "$BUILD_TYPE" = "production" ]; then
    echo "构建前端应用..."
    
    if [ ! -d "frontend/node_modules" ]; then
        echo "安装前端依赖..."
        cd frontend
        npm install
        cd ..
    fi
    
    cd frontend
    npm run build
    cd ..
    
    echo "前端构建完成"
fi

# 构建Docker镜像
echo "构建Docker镜像..."

if [ "$BUILD_TYPE" = "development" ]; then
    # 开发环境：使用docker-compose
    echo "使用docker-compose构建开发环境..."
    docker-compose build
else
    # 生产环境：构建一体化镜像
    echo "构建生产环境一体化镜像..."
    docker build -t $FULL_IMAGE_NAME .
fi

echo "Docker镜像构建完成: $FULL_IMAGE_NAME"

# 推送镜像（如果需要）
if [ "$PUSH_IMAGE" = "true" ]; then
    echo "推送镜像到仓库..."
    docker push $FULL_IMAGE_NAME
    echo "镜像推送完成"
fi

# 显示镜像信息
echo "=========================================="
echo "构建完成！"
echo "镜像: $FULL_IMAGE_NAME"
echo "大小: $(docker images $FULL_IMAGE_NAME --format "table {{.Size}}" | tail -n 1)"
echo "=========================================="

# 提供启动命令
if [ "$BUILD_TYPE" = "development" ]; then
    echo "开发环境启动命令:"
    echo "docker-compose up -d"
    echo ""
    echo "访问地址:"
    echo "前端: http://localhost:3000"
    echo "后端: http://localhost:8100"
else
    echo "生产环境启动命令:"
    echo "docker run -d \\"
    echo "  --name algorithm-platform \\"
    echo "  -p 3000:3000 \\"
    echo "  -p 8100:8100 \\"
    echo "  -v /var/run/docker.sock:/var/run/docker.sock \\"
    echo "  -v algorithm-data:/app/data \\"
    echo "  $FULL_IMAGE_NAME"
    echo ""
    echo "访问地址:"
    echo "前端: http://localhost:3000"
    echo "后端: http://localhost:8100"
    echo "API文档: http://localhost:8100/docs"
fi

echo "=========================================="
