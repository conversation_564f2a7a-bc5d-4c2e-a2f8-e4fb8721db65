#!/bin/bash

# 算法管理平台启动脚本

set -e

echo "=========================================="
echo "算法管理平台启动中..."
echo "=========================================="

# 检查必要的目录
echo "检查数据目录..."
mkdir -p /app/data/algorithms /app/data/uploads /app/logs

# 检查Docker socket
if [ ! -S /var/run/docker.sock ]; then
    echo "警告: Docker socket未挂载，容器管理功能将不可用"
    echo "请使用: -v /var/run/docker.sock:/var/run/docker.sock"
fi

# 设置环境变量
export PYTHONPATH=/app
export DATA_DIR=/app/data

# 启动后端API服务
echo "启动后端API服务..."
cd /app
python src/main.py --host 0.0.0.0 --port 8100 &
BACKEND_PID=$!

# 等待后端服务启动
echo "等待后端服务启动..."
for i in {1..30}; do
    if curl -f http://localhost:8100/health >/dev/null 2>&1; then
        echo "后端服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "后端服务启动超时"
        exit 1
    fi
    sleep 2
done

# 启动Nginx（前端服务）
echo "启动前端服务..."
nginx -g "daemon off;" &
NGINX_PID=$!

# 等待前端服务启动
echo "等待前端服务启动..."
for i in {1..15}; do
    if curl -f http://localhost:3000/ >/dev/null 2>&1; then
        echo "前端服务启动成功"
        break
    fi
    if [ $i -eq 15 ]; then
        echo "前端服务启动超时"
        exit 1
    fi
    sleep 2
done

echo "=========================================="
echo "算法管理平台启动完成！"
echo "前端界面: http://localhost:3000"
echo "后端API: http://localhost:8100"
echo "API文档: http://localhost:8100/docs"
echo "=========================================="

# 监控进程
monitor_processes() {
    while true; do
        # 检查后端进程
        if ! kill -0 $BACKEND_PID 2>/dev/null; then
            echo "后端服务异常退出，重启中..."
            cd /app
            python src/main.py --host 0.0.0.0 --port 8100 &
            BACKEND_PID=$!
        fi
        
        # 检查Nginx进程
        if ! kill -0 $NGINX_PID 2>/dev/null; then
            echo "前端服务异常退出，重启中..."
            nginx -g "daemon off;" &
            NGINX_PID=$!
        fi
        
        sleep 10
    done
}

# 信号处理
cleanup() {
    echo "收到停止信号，正在关闭服务..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $NGINX_PID 2>/dev/null || true
    wait
    echo "服务已关闭"
    exit 0
}

trap cleanup SIGTERM SIGINT

# 开始监控
monitor_processes
