#!/bin/bash

# 算法管理平台开发环境设置脚本

set -e

echo "=========================================="
echo "算法管理平台开发环境设置"
echo "=========================================="

# 检查必要工具
check_requirements() {
    echo "检查开发环境要求..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3.11+ 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version < 3.11" | bc -l) -eq 1 ]]; then
        echo "❌ Python版本过低，需要3.11+，当前版本: $python_version"
        exit 1
    fi
    echo "✅ Python版本: $python_version"
    
    # 检查uv
    if ! command -v uv &> /dev/null; then
        echo "⚠️  uv未安装，正在安装..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        source $HOME/.cargo/env
    fi
    echo "✅ uv已安装: $(uv --version)"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 18+ 未安装"
        exit 1
    fi
    
    node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 18 ]]; then
        echo "❌ Node.js版本过低，需要18+，当前版本: v$node_version"
        exit 1
    fi
    echo "✅ Node.js版本: $(node --version)"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker未安装"
        exit 1
    fi
    echo "✅ Docker已安装: $(docker --version)"
}

# 设置后端环境
setup_backend() {
    echo ""
    echo "设置后端Python环境..."
    
    cd backend
    
    # 创建虚拟环境
    if [ ! -d ".venv" ]; then
        echo "创建Python虚拟环境..."
        uv venv --python 3.11
    fi
    
    # 激活虚拟环境并安装依赖
    echo "安装Python依赖..."
    source .venv/bin/activate
    uv pip install -e ".[dev,test]"
    
    # 设置pre-commit hooks
    if [ -f ".pre-commit-config.yaml" ]; then
        echo "设置pre-commit hooks..."
        pre-commit install
    fi
    
    echo "✅ 后端环境设置完成"
    cd ..
}

# 设置前端环境
setup_frontend() {
    echo ""
    echo "设置前端Node.js环境..."
    
    cd frontend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "安装Node.js依赖..."
        npm install
    else
        echo "更新Node.js依赖..."
        npm update
    fi
    
    echo "✅ 前端环境设置完成"
    cd ..
}

# 创建开发配置文件
create_dev_configs() {
    echo ""
    echo "创建开发配置文件..."
    
    # 创建环境变量文件
    if [ ! -f ".env.development" ]; then
        cat > .env.development << EOF
# 开发环境配置
NODE_ENV=development
DEBUG=true

# 后端配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8100
DATABASE_URL=sqlite:///./data/algorithm_platform.db

# 前端配置
VITE_API_BASE_URL=http://localhost:8100
VITE_WS_URL=ws://localhost:8100

# Docker配置
DOCKER_HOST=unix:///var/run/docker.sock

# 日志配置
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed
EOF
        echo "✅ 创建开发环境配置文件"
    fi
    
    # 创建数据目录
    mkdir -p data logs
    echo "✅ 创建数据和日志目录"
}

# 运行初始化测试
run_initial_tests() {
    echo ""
    echo "运行初始化测试..."
    
    # 测试后端
    cd backend
    source .venv/bin/activate
    
    echo "测试后端导入..."
    python -c "
try:
    import src.main
    print('✅ 后端模块导入成功')
except ImportError as e:
    print(f'❌ 后端模块导入失败: {e}')
    exit(1)
"
    
    cd ../frontend
    
    echo "测试前端构建..."
    if npm run build > /dev/null 2>&1; then
        echo "✅ 前端构建测试成功"
    else
        echo "❌ 前端构建测试失败"
    fi
    
    cd ..
}

# 显示开发指南
show_dev_guide() {
    echo ""
    echo "=========================================="
    echo "🎉 开发环境设置完成！"
    echo "=========================================="
    echo ""
    echo "开发命令:"
    echo ""
    echo "# 启动后端开发服务器"
    echo "cd backend && source .venv/bin/activate && python src/main.py --reload"
    echo ""
    echo "# 启动前端开发服务器"
    echo "cd frontend && npm run dev"
    echo ""
    echo "# 运行测试"
    echo "cd backend && source .venv/bin/activate && pytest"
    echo ""
    echo "# 代码格式化"
    echo "cd backend && source .venv/bin/activate && black src/ && isort src/"
    echo ""
    echo "# 类型检查"
    echo "cd backend && source .venv/bin/activate && mypy src/"
    echo ""
    echo "访问地址:"
    echo "- 前端开发服务器: http://localhost:3000"
    echo "- 后端API服务器: http://localhost:8100"
    echo "- API文档: http://localhost:8100/docs"
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    check_requirements
    setup_backend
    setup_frontend
    create_dev_configs
    run_initial_tests
    show_dev_guide
}

# 运行主函数
main "$@"
