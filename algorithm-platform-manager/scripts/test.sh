#!/bin/bash

# 算法管理平台测试脚本

set -e

echo "=========================================="
echo "算法管理平台测试脚本"
echo "=========================================="

# 配置
BACKEND_URL="http://localhost:8100"
FRONTEND_URL="http://localhost:3000"
TEST_IMAGE="data/test_face.jpg"

# 检查服务是否运行
check_service() {
    local url=$1
    local name=$2
    
    echo "检查 $name 服务..."
    
    for i in {1..30}; do
        if curl -f "$url/health" >/dev/null 2>&1; then
            echo "✅ $name 服务运行正常"
            return 0
        fi
        
        if [ $i -eq 30 ]; then
            echo "❌ $name 服务不可用"
            return 1
        fi
        
        sleep 2
    done
}

# 测试API接口
test_api() {
    echo "测试API接口..."
    
    # 测试健康检查
    echo "测试健康检查..."
    response=$(curl -s "$BACKEND_URL/health")
    if echo "$response" | grep -q "healthy"; then
        echo "✅ 健康检查通过"
    else
        echo "❌ 健康检查失败"
        return 1
    fi
    
    # 测试算法列表
    echo "测试算法列表..."
    response=$(curl -s "$BACKEND_URL/api/algorithms/")
    if echo "$response" | grep -q "success"; then
        echo "✅ 算法列表接口正常"
    else
        echo "❌ 算法列表接口失败"
        return 1
    fi
    
    # 测试系统配置
    echo "测试系统配置..."
    response=$(curl -s "$BACKEND_URL/api/system/config")
    if echo "$response" | grep -q "success"; then
        echo "✅ 系统配置接口正常"
    else
        echo "❌ 系统配置接口失败"
        return 1
    fi
    
    # 测试系统统计
    echo "测试系统统计..."
    response=$(curl -s "$BACKEND_URL/api/system/stats")
    if echo "$response" | grep -q "success"; then
        echo "✅ 系统统计接口正常"
    else
        echo "❌ 系统统计接口失败"
        return 1
    fi
    
    echo "✅ 所有API测试通过"
}

# 测试前端页面
test_frontend() {
    echo "测试前端页面..."
    
    # 测试主页
    echo "测试主页..."
    if curl -f "$FRONTEND_URL/" >/dev/null 2>&1; then
        echo "✅ 前端主页可访问"
    else
        echo "❌ 前端主页不可访问"
        return 1
    fi
    
    # 测试静态资源
    echo "测试静态资源..."
    if curl -f "$FRONTEND_URL/assets/" >/dev/null 2>&1 || curl -f "$FRONTEND_URL/static/" >/dev/null 2>&1; then
        echo "✅ 静态资源可访问"
    else
        echo "⚠️  静态资源路径可能不存在（正常情况）"
    fi
    
    echo "✅ 前端测试通过"
}

# 性能测试
performance_test() {
    echo "性能测试..."
    
    # 测试API响应时间
    echo "测试API响应时间..."
    start_time=$(date +%s%N)
    curl -s "$BACKEND_URL/api/algorithms/" >/dev/null
    end_time=$(date +%s%N)
    
    response_time=$(( (end_time - start_time) / 1000000 ))
    echo "API响应时间: ${response_time}ms"
    
    if [ $response_time -lt 1000 ]; then
        echo "✅ API响应时间良好"
    else
        echo "⚠️  API响应时间较慢"
    fi
    
    # 测试并发请求
    echo "测试并发请求..."
    for i in {1..5}; do
        curl -s "$BACKEND_URL/health" >/dev/null &
    done
    wait
    echo "✅ 并发请求测试完成"
}

# 集成测试
integration_test() {
    echo "集成测试..."
    
    # 如果有测试算法包，进行完整流程测试
    if [ -f "test/test_algorithm.tar.gz" ]; then
        echo "测试算法包上传..."
        response=$(curl -s -X POST \
            -F "file=@test/test_algorithm.tar.gz" \
            "$BACKEND_URL/api/algorithms/upload")
        
        if echo "$response" | grep -q "success"; then
            echo "✅ 算法包上传测试通过"
        else
            echo "❌ 算法包上传测试失败"
        fi
    else
        echo "⚠️  未找到测试算法包，跳过上传测试"
    fi
}

# 主测试流程
main() {
    echo "开始测试算法管理平台..."
    
    # 检查服务状态
    if ! check_service "$BACKEND_URL" "后端"; then
        echo "❌ 后端服务不可用，请先启动服务"
        exit 1
    fi
    
    if ! check_service "$FRONTEND_URL" "前端"; then
        echo "❌ 前端服务不可用，请先启动服务"
        exit 1
    fi
    
    # 运行测试
    test_api
    test_frontend
    performance_test
    integration_test
    
    echo "=========================================="
    echo "✅ 所有测试完成！"
    echo "=========================================="
    
    # 显示测试报告
    echo "测试报告:"
    echo "- 后端服务: ✅ 正常"
    echo "- 前端服务: ✅ 正常"
    echo "- API接口: ✅ 正常"
    echo "- 性能测试: ✅ 通过"
    echo ""
    echo "访问地址:"
    echo "- 前端界面: $FRONTEND_URL"
    echo "- 后端API: $BACKEND_URL"
    echo "- API文档: $BACKEND_URL/docs"
    echo "=========================================="
}

# 运行测试
main "$@"
