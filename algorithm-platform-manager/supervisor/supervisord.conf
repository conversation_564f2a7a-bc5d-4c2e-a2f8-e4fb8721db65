[supervisord]
nodaemon=true
user=appuser
logfile=/app/logs/supervisord.log
pidfile=/var/run/supervisord.pid

[program:backend]
command=python src/main.py --host 0.0.0.0 --port 8100
directory=/app
user=appuser
autostart=true
autorestart=true
stderr_logfile=/app/logs/backend_error.log
stdout_logfile=/app/logs/backend_access.log
environment=PYTHONPATH="/app",DATA_DIR="/app/data"

[program:nginx]
command=nginx -g "daemon off;"
user=appuser
autostart=true
autorestart=true
stderr_logfile=/app/logs/nginx_error.log
stdout_logfile=/app/logs/nginx_access.log

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700
chown=appuser:appuser

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
