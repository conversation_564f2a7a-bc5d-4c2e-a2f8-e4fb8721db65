#!/bin/bash

# 算法管理平台专业版启动脚本

set -e

echo "=========================================="
echo "🚀 算法管理平台专业版"
echo "=========================================="

# 检查依赖
check_dependencies() {
    echo "检查系统依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 未安装"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装"
        exit 1
    fi
    
    # 检查uv
    if ! command -v uv &> /dev/null; then
        echo "📦 安装uv包管理器..."
        pip install uv
    fi
    
    echo "✅ 依赖检查完成"
}

# 设置后端环境
setup_backend() {
    echo "设置后端环境..."
    
    cd backend
    
    # 创建uv虚拟环境
    if [ ! -d ".venv" ]; then
        echo "创建Python虚拟环境..."
        uv venv --python 3.11
    fi
    
    # 激活虚拟环境并安装依赖
    echo "安装Python依赖..."
    source .venv/bin/activate
    uv pip install -r requirements.txt
    
    echo "✅ 后端环境设置完成"
    cd ..
}

# 设置前端环境
setup_frontend() {
    echo "设置前端环境..."
    
    cd frontend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "安装Node.js依赖..."
        npm install
    fi
    
    echo "✅ 前端环境设置完成"
    cd ..
}

# 启动服务
start_services() {
    echo "启动服务..."
    
    # 创建必要的目录
    mkdir -p data logs
    
    # 启动后端服务
    echo "🔥 启动后端服务..."
    cd backend
    source .venv/bin/activate
    python src/main_professional.py --host 0.0.0.0 --port 8100 &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 启动前端服务
    echo "🌐 启动前端服务..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo ""
    echo "✅ 服务启动完成！"
    echo ""
    echo "🎯 访问地址:"
    echo "   - 前端界面: http://localhost:3000"
    echo "   - 后端API: http://localhost:8100"
    echo "   - API文档: http://localhost:8100/docs"
    echo ""
    echo "🔧 专业特性:"
    echo "   ✅ uv虚拟环境管理"
    echo "   ✅ Vue 3 + Vite前端"
    echo "   ✅ FastAPI后端"
    echo "   ✅ 专业化错误处理"
    echo "   ✅ 完整的日志系统"
    echo ""
    echo "按 Ctrl+C 停止服务"
    
    # 等待用户中断
    trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo "👋 服务已停止"; exit 0' INT
    wait
}

# 主函数
main() {
    case "${1:-start}" in
        "setup")
            check_dependencies
            setup_backend
            setup_frontend
            echo "🎉 环境设置完成！运行 './start.sh' 启动服务"
            ;;
        "start")
            check_dependencies
            setup_backend
            setup_frontend
            start_services
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  setup    只设置环境，不启动服务"
            echo "  start    设置环境并启动服务 (默认)"
            echo "  help     显示此帮助信息"
            ;;
        *)
            echo "未知命令: $1"
            echo "运行 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

main "$@"
